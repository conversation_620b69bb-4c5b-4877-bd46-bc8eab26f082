[2025-03-31 10:31:20]:
Good morning, morning, morning.
早上好，早上好，早上好。


[2025-03-31 10:31:28]:
How's everyone?
大家都好吗？


[2025-03-31 10:31:32]:
And not so good because my cats got into a fight last time so I went to buy the little late.
但不太好，因为我的猫上次打架了，所以我去买了这个小晚。


[2025-03-31 10:31:43]:
How many cats have you caught? 3?
你抓了多少只猫？3?


[2025-03-31 10:31:56]:
That sounds like a lot of work.
这听起来像是很多工作。


[2025-03-31 10:31:59]:
Yes.
是的。


[2025-03-31 10:32:08]:
OK.
还行。


[2025-03-31 10:32:13]:
Let me share my screen now.
现在让我分享我的屏幕。


[2025-03-31 10:32:17]:
OK, send the request.
好，发送请求。


[2025-03-31 10:32:43]:
Is it working?
它有效吗？


[2025-03-31 10:32:47]:
Yeah, OK. I'm looking at your desktop.
是的，好的。我在看你的桌面。


[2025-03-31 10:32:51]:
And the apps.
还有应用程序。


[2025-03-31 10:33:04]:
MMM
嗯


[2025-03-31 10:33:05]:
Since our last meeting I made.
自从我们上次见面以来，我就做了。


[2025-03-31 10:33:10]:
Some small improvements to my project. I ate the real time feature and visitor can check their online before they take a photo.
对我的项目进行了一些小的改进。我吃了实时功能，访问者可以在拍照前在线查看。


[2025-03-31 10:33:29]:
But.
但。


[2025-03-31 10:33:32]:
MMM and can like the user, quickly confirm confirm their post before getting the.
MMM 并可以点赞用户，在获取之前快速确认确认他们的帖子。


[2025-03-31 10:33:43]:
The result?
结果如何？


[2025-03-31 10:33:47]:
And.
和。


[2025-03-31 10:33:49]:
Take the photo.
拍摄照片。


[2025-03-31 10:33:58]:
Yet now the system can give you an actual match result instead of just a mock up.
然而，现在系统可以为您提供实际的比赛结果，而不仅仅是一个模型。


[2025-03-31 10:34:07]:
But that I ran into a new problem, the animal out.
但是我遇到了一个新问题，动物出来了。


[2025-03-31 10:34:15]:
Don't always match well with their actual photos.
并不总是与他们的实际照片匹配。


[2025-03-31 10:34:20]:
But I I'm not sure why.
但我不知道为什么。


[2025-03-31 10:34:22]:
Here's my outline.
这是我的大纲。


[2025-03-31 10:34:27]:
How many different photos do you have?
您有多少张不同的照片？


[2025-03-31 10:34:32]:
22000.
22000.


[2025-03-31 10:34:37]:
And 10 different classes of the animals.
以及 10 种不同类别的动物。


[2025-03-31 10:34:45]:
OK. I see. It's only a 57% match, I guess.
好的，我明白了。我猜只有 57% 的匹配。


[2025-03-31 10:34:50]:
So that's the, that's the, that's the best match that I can find in the database of 2000 photos. Is that yeah, maybe, yeah. And I, I'm not sure maybe it could be because I'm using like a older yellow model, yellow 8 and now there, there's already yellow 11 available, so.
所以这就是，这就是，这是我在 2000 张照片的数据库中能找到的最佳匹配。是的，也许，是的。而我，我不确定可能是因为我正在使用旧的黄色型号，黄色 8，现在已经有黄色 11 可用，所以。


[2025-03-31 10:35:14]:
OK, so how come you're using the older one? Yeah, I'm still working on this issue.
好的，那么你怎么用旧的呢？是的，我还在努力解决这个问题。


[2025-03-31 10:35:22]:
So I guess I need to function the animal model again when I finalize the animal database.
所以我想当我完成动物数据库时，我需要再次运行动物模型。


[2025-03-31 10:35:35]:
And you just got a couple of questions in the chat.
您刚刚在聊天中收到了几个问题。


[2025-03-31 10:35:40]:
Yes.
是的。


[2025-03-31 10:35:43]:
Actually, it's one question about the animal outline. Not much of the animal picture. Not my picture.
实际上，这是一个关于动物轮廓的问题。没有太多的动物图片。不是我的照片。


[2025-03-31 10:36:01]:
And I.
还有我。


[2025-03-31 10:36:05]:
Also added two small fixtures we talked about before, the animal information and the location in the museum.
还添加了我们之前谈到的两个小固定装置，动物信息和博物馆中的位置。


[2025-03-31 10:36:18]:
But just a mock and.
但只是一个模拟和。


[2025-03-31 10:36:26]:
If you actually use the map, but I didn't get the museum map so it's just a mop to and.
如果你真的使用地图，但我没有得到博物馆地图，所以它只是一个拖把。


[2025-03-31 10:36:38]:
And when the visitor click the save results, they can write their emails here and send the e-mail.
当访问者单击保存结果时，他们可以在此处编写电子邮件并发送电子邮件。


[2025-03-31 10:36:49]:
Yeah, that's all from my this week.
是的，这就是我本周的全部内容。


[2025-03-31 10:36:59]:
This is great. I like the outline cause Andrew mentioned last week about thinking about making the online dynamic and then you made it. I think it's great especially if you look at your post so allies and it took me a few seconds to understand the outline of the cat because I can see that the outlines in a similar shape as the cat but you.
这太好了。我喜欢 Andrew 上周提到的关于考虑建立在线动态的大纲，然后你做到了。我认为这很棒，特别是如果你看你的帖子，那么盟友们，我花了几秒钟才理解了猫的轮廓，因为我可以看到轮廓的形状与猫相似，但你除外。


[2025-03-31 10:37:29]:
The online into the middle of the picture, so it's a bit detached from the cat, but I mean the online feature is great.
在线进入图片的中间，所以它与猫有点脱节，但我的意思是在线功能很棒。


[2025-03-31 10:37:42]:
And currently I'm just thinking, how can we maybe because you added a feature of animal information and locating the exhibit in the museum as a way to engage visitors, right? So I'm just thinking maybe we can.
现在我只是在想，我们怎么能呢，因为你添加了动物信息的功能，并将展品放在博物馆中，以此来吸引参观者，对吧？所以我只是在想，也许我们可以。


[2025-03-31 10:38:06]:
Try to take pictures of the exhibits and also somehow, as you said, find a map of the museum and so that you can integrate.
尝试为展品拍照，并且正如您所说，以某种方式找到博物馆的地图，以便您可以融入。


[2025-03-31 10:38:18]:
The location finding feature using the actual map of the museum.
使用博物馆实际地图的定位功能。


[2025-03-31 10:38:25]:
Yeah, Aunt, that's was that was my thing think.
是的，阿姨，这就是我的想法。


[2025-03-31 10:38:30]:
Yes, I think I need to take photo of the animal museum and to fan fenchen. The models maybe will be better.
是的，我觉得我需要拍动物博物馆的照片，给芬辰拍照。模型可能会更好。


[2025-03-31 10:38:40]:
Yeah. And for that, I think we need to.
是的。为此，我认为我们需要这样做。


[2025-03-31 10:38:45]:
I mean, there's a lot of work that needs to be done before we can do that because we need to talk to the exhibition branch of the museum and then they might need to.
我的意思是，在我们这样做之前，还有很多工作要做，因为我们需要与博物馆的展览部门交谈，然后他们可能需要这样做。


[2025-03-31 10:38:58]:
Grant us approval as well, so.
也请批准我们，所以。


[2025-03-31 10:39:04]:
I'm also thinking whether there's any like walk around that we can build this prototype first again, like using like a fake map or something.
我也在想是否有任何类似 walk around 的方法，我们可以先再次构建这个原型，比如使用类似假地图之类的东西。


[2025-03-31 10:39:27]:
I download. Actually I downloaded the.
我下载。其实我下载了。


[2025-03-31 10:39:31]:
Map from the museum website but you found everything. But I'm not sure why the picture isn't showing on the website.
博物馆网站上的地图，但您找到了所有东西。但我不确定为什么这张照片没有显示在网站上。


[2025-03-31 10:39:57]:
Right. And I think they have a top down view of the room.
右。我认为他们有房间的自上而下的视图。


[2025-03-31 10:40:04]:
I've only seen some.
我只见过一些。


[2025-03-31 10:40:07]:
Some pictures showing the top down view. Yes, that one.
一些图片显示了自上而下的视图。是的，就是那个。


[2025-03-31 10:40:42]:
But we like use the map.
但我们喜欢使用地图。


[2025-03-31 10:40:47]:
We need to like.
我们需要喜欢。


[2025-03-31 10:41:03]:
We need like more details of the gallery, different gallery in the museum, like maybe there is a dinosaur or maybe there is a bird or something else.
我们需要画廊的更多细节，博物馆里的不同画廊，比如也许有一只恐龙，也许有一只鸟或其他东西。


[2025-03-31 10:41:21]:
And if you are going into the museum and take pictures of the exhibit and make it your data set and then you will need to do I guess the mapping, the mapping between.
如果你要进入博物馆，为展览拍照，并将其作为你的数据集，然后你需要做映射，我猜，映射之间的映射。


[2025-03-31 10:41:34]:
The exhibits that you've taken picture of and where they are based on this map and maybe you need little pinpoint OK on this map.
您根据这张地图拍摄的展品及其位置，也许您需要在这张地图上确定一点点。


[2025-03-31 10:41:55]:
So my next step is to take take picture from the museum.
所以我的下一步是从博物馆拍照。


[2025-03-31 10:42:04]:
And.
和。


[2025-03-31 10:42:06]:
Yeah. Maybe you can try that. Maybe you can arrange, we can arrange a time this week and then you can come to museum and I can maybe do this with you together to see if this is doable, OK.
是的。也许你可以试试。也许你可以安排，我们可以在这周安排一个时间，然后你可以来博物馆，我也许可以和你一起做这件事，看看这是否可行，好的。


[2025-03-31 10:42:21]:
And it sounds like fun. It doesn't have to be the whole museum. Maybe it's just one exhibition hall.
这听起来很有趣。它不一定是整个博物馆。也许它只是一个展厅。


[2025-03-31 10:42:33]:
I'm sorry. We'll see
对不起。我们拭目以待


[2025-03-31 10:42:36]:
OK.
还行。


[2025-03-31 10:42:39]:
Sounds like fun, Andrew, I understand.
听起来很有趣，Andrew，我明白了。


[2025-03-31 10:42:44]:
How many photos? At the moment you've got a database with 2000 animal photos in it and where are they from? Are they? I mean, that cat won't look like it could be one of your cats from the cargo.
多少张照片？目前，您有一个包含 2000 张动物照片的数据库，它们来自哪里？是吗？我的意思是，那只猫看起来不会像是你货物中的一只猫。


[2025-03-31 10:43:02]:
From the what, Sir Taco? Oh, it's a like a open resource. Not this one, this one.
从什么，塔科爵士？哦，这就像一个开放资源。不是这个，这个。


[2025-03-31 10:43:13]:
It's the like I don't know how it's a website of the deep learning. You can find some open resource from the website.
就像我不知道它是怎么一个深度学习的网站。您可以从网站上找到一些开放资源。


[2025-03-31 10:43:28]:
So that's basically just a whole bunch of photos that someone's prepared and uploaded. Yeah. And then I guess the photo from the Cargo official.
所以这基本上就是某人准备和上传的一大堆照片。是的。然后我猜到了 Cargo 官员的照片。


[2025-03-31 10:43:43]:
Can you show me some of the photos just so I get a sense of?
您能给我看一些照片，以便我了解一下吗？


[2025-03-31 10:43:49]:
What they look like.
他们长什么样子。


[2025-03-31 10:43:53]:
Initially I thought it was your cat, your heart. Me too. Basically, it was one of yours.
起初我以为是你的猫，你的心。我也是。基本上，这是你的一个。


[2025-03-31 10:44:01]:
Oh.
哦。


[2025-03-31 10:44:12]:
Yes.
是的。


[2025-03-31 10:44:31]:
Oh, it's 20,020 thousand, Yeah.
哦，是 20,020,000，是的。


[2025-03-31 10:44:41]:
And the outline is the outline of the animal included in the data set Or have you somehow gone through and figured out the outline for each of them and added that to your data set yourself? Or I used to the Euro 8 and this image is all like.
大纲是数据集中包含的动物的轮廓，或者您是否以某种方式浏览并找出了每个动物的轮廓，并自己将其添加到您的数据集中？或者我习惯了 Euro 8，这张图片都是这样的。


[2025-03-31 10:45:03]:
I just put the put the all images to use the yellow light and.
我只是把所有图像都放在使用黄光和。


[2025-03-31 10:45:13]:
OK, so you generated the data, the outline data for all 20,000 using yellow.
好了，您生成了数据，所有 20,000 个的轮廓数据都使用黄色。


[2025-03-31 10:45:21]:
OK, but know it's a an older.
好吧，但要知道这是一个更老的。


[2025-03-31 10:45:27]:
Wei Shen, Sir.
Wei Shen， 先生。


[2025-03-31 10:45:31]:
OK, the reason I'm asking is just I think if you're taking photos in the museum.
好吧，我问的原因只是我想你是否在博物馆里拍照。


[2025-03-31 10:45:39]:
To take 20,000 photos, which is what you need to get. I mean, I think if you're finding even like with the 20,000 images that the best match like from that pose that you had before is like 57%.
拍摄 20,000 张照片，这就是你需要得到的。我的意思是，我认为如果你发现 20,000 张图片中，你之前那个姿势的最佳匹配度是 57%。


[2025-03-31 10:45:54]:
I just worry that if you have, say, 100 photos from the museum and you take yourself.
我只是担心，如果你有 100 张来自博物馆的照片，然后你自己拍。


[2025-03-31 10:46:02]:
You're not gonna have enough to have really good matches for anything or not many.
你不会有足够的钱来匹配任何东西，或者没有很多。


[2025-03-31 10:46:08]:
And the user experience might be a bit frustrating.
用户体验可能有点令人沮丧。


[2025-03-31 10:46:13]:
I think also you can have a good suggestion before.
我想你之前也可以有一个很好的建议。


[2025-03-31 10:46:19]:
Even maybe if we can restrict the background a bit so we have people stand be like if you got a camera in a space and maybe the background is black or white or something and then we say tell people to stand and put their footprints in a certain area, Then we can probably get more a better quality outlines or maybe.
即使我们可以稍微限制一下背景，让人们站着，就像你在某个空间里有一台相机，也许背景是黑色或白色或其他什么，然后我们说让人们站着，把他们的脚印放在某个区域，那么我们可能可以得到更多、质量更好的轮廓，或者也许。


[2025-03-31 10:46:43]:
A bit more.
再多一点。


[2025-03-31 10:46:47]:
Accurate somehow.
不知何故是准确的。


[2025-03-31 10:46:50]:
Yeah, agree. Although thinking about it, that wouldn't fix the match problem that would. I have one related question about the matching algorithm. Do you know based on how did the, how did your program pick the best match image?
是的，同意。尽管仔细想想，这并不能解决匹配问题。我有一个关于 matching 算法的相关问题。您知道吗，您的程序是如何选择最匹配的图像的？


[2025-03-31 10:47:15]:
This might be a very big question, but I used to like I'll just 7%.
这可能是一个非常大的问题，但我以前喜欢我只会 7%。


[2025-03-31 10:47:23]:
Like.
喜欢。


[2025-03-31 10:47:32]:
Yes, like I is when you take photo of it and the model will know you outlaw of the visitor and the model will find the.
是的，就像我一样，当你拍下它时，模特会知道你是访客的非法之徒，模特会找到。


[2025-03-31 10:47:50]:
Use the IOU to find the. I'm not sure.
使用 IOU 查找。我不确定。


[2025-03-31 10:47:58]:
Yes, sorry.
是的，对不起。


[2025-03-31 10:48:01]:
And.
和。


[2025-03-31 10:48:04]:
The model will use the IOU to fund the best match from the database.
该模型将使用 IOU 为数据库中的最佳匹配提供资金。


[2025-03-31 10:48:20]:
Does it detect or recognize what animal it is?
它能检测或识别它是什么动物吗？


[2025-03-31 10:48:24]:
Before match.
比赛前。


[2025-03-31 10:48:28]:
I guess it will.
我想会的。


[2025-03-31 10:48:32]:
Find the picture before they know the name of the animals. So we have found the picture 1st and when they when the model found the picture it will like a label of the picture the animal names right? So and when they find the when the model find the picture it will know the name of the picture.
在他们知道动物的名字之前找到图片。所以我们找到了第一张图片，当他们找到图片时，当模型找到图片时，它会像图片的标签一样，动物的名字对吧？所以，当他们找到时，当模型找到图片时，它会知道图片的名称。


[2025-03-31 10:49:01]:
OK then so if it's before it recognize what animal it is then maybe you could for the exhibition pictures it could already pick one from the exited that's best matching your post.
好的，那么，如果它在识别出它是什么动物之前，那么也许您可以为展览图片选择一张最符合您的帖子的图片。


[2025-03-31 10:49:18]:
And then you could internally recognize what animal it is.
然后你就可以在内心认出它是什么动物。


[2025-03-31 10:49:24]:
I bought exhibition animal. It is using its own big data set for detecting what animal.
我买了展览动物。它使用自己的大数据集来检测什么动物。


[2025-03-31 10:49:33]:
After I don't know if I'm making any sense, but is this oh like the model found the animal from the database and will show the picture from the museum? No, it's the other way. It's that it will first detect.
之后我不知道我说的有没有道理，但这是不是像模型从数据库中找到了动物，并会显示博物馆的图片？不，这是相反的。而是它会首先检测到。


[2025-03-31 10:49:55]:
Which image in the exhibition, in the exhibits that best match your outline? And then internally, if you pick that picture, pick the exhibit and then you will continue to recognize what animal the exhibit is.
展览中哪张图片、展品中最符合您的轮廓？然后在内部，如果你选择那张图片，选择展品，然后你将继续识别这个展品是什么动物。


[2025-03-31 10:50:15]:
Based on the 20,000 animal images that it has.
基于它拥有的 20,000 张动物图像。


[2025-03-31 10:50:25]:
I'm sorry, I don't mean to gift, OK? I'm just making a suggestion. If I think if you, if we know, I'm sorry my headphones are doing something weird. If you're, if we know what.
对不起，我不是故意送礼的，好吗？我只是提出一个建议。如果我想如果你，如果我们知道，我很抱歉我的耳机在做一些奇怪的事情。如果你是，如果我们知道什么。


[2025-03-31 10:50:41]:
We should know what the image is all right, So we could easily when we take the we could.
我们应该知道什么图像是正确的，所以当我们拍摄时我们可以很容易地。


[2025-03-31 10:50:46]:
Let me get labeled photos after we take them so we could pre analyze them, right? And so in the database, it could just be, yeah, we know this is a Lima, this is a pigeon, whatever.
拍摄后，让我获取带标签的照片，以便我们可以预先分析它们，对吧？所以在数据库中，它可能只是，是的，我们知道这是一羽利马，这是一羽鸽子，等等。


[2025-03-31 10:51:01]:
Yeah, and what animal to label?
是的，要给什么动物贴标签呢？


[2025-03-31 10:51:04]:
That animal will be picked from the exhibits that you take from the museum.
该动物将从您从博物馆带走的展品中挑选。


[2025-03-31 10:51:10]:
And then when you label it, you use, you train the data using the data sets, the online data set, yeah.
然后，当你标记它时，你使用，你使用数据集训练数据，在线数据集，是的。


[2025-03-31 10:51:20]:
Thanks, Andrew for meetings terminology, OK.
感谢 Andrew 的会议术语，好的。


[2025-03-31 10:51:37]:
But I don't know if what I just suggested matched with.
但我不知道我刚才的建议是否匹配。


[2025-03-31 10:51:42]:
The flow that you originally have here.
您最初在此处拥有的流程。


[2025-03-31 10:51:46]:
Yeah, I'm not quite sure I fully understand it yet either. So either.
是的，我也不太确定我是否完全理解它。所以。


[2025-03-31 10:51:55]:
Let me try. Can I try and understand? So you say take a photo of the person and then find the outline? Oh, yes. Find the best matching outline. Yeah. In the photos of the museum. Yeah.
让我试试。我可以尝试理解吗？所以你说给这个人拍一张照片，然后找到轮廓？哦，是的。找到最佳匹配的轮廓。是的。在博物馆的照片中。是的。


[2025-03-31 10:52:10]:
And then this is where I get crazy for me. Then we know what that photo is already 'cause we pre analyzed it.
然后这就是我为我疯狂的地方。然后我们知道那张照片已经是什么了，因为我们预先分析了它。


[2025-03-31 10:52:19]:
So we won the best match and it's a photo of a pigeon. And then we can look in the database and say, yes, we know this is a pigeon, this photo.
所以我们赢得了最好的比赛，这是一张鸽子的照片。然后我们可以查看数据库并说，是的，我们知道这是一羽鸽子，这张照片。


[2025-03-31 10:52:26]:
That we have photo and we have label.
我们有照片，我们有标签。


[2025-03-31 10:52:30]:
I got it. Or are you saying that we don't, I'll wake up, We don't have that way at all that I think it's what Andrew said.
知道了。或者你是说我们没有，我会醒来的，我们根本没有那种方式，我认为这是安德鲁所说的。


[2025-03-31 10:52:43]:
I can try. You got me. Yes, I wake up. Sorry.
我可以试试。你明白了。是的，我醒了。不好意思。


[2025-03-31 10:52:51]:
I ask you whether know it detect recognize what animal it is and how it calculates online because I want to know where the magic happened, you know where the deep learning training happened and if it's if it happened after you calculate the best matching online then.
我问你是否知道它检测识别它是什么动物以及它是如何在线计算的，因为我想知道魔法发生在哪里，你知道深度学习训练发生在哪里，如果它发生在你在线计算最佳匹配之后。


[2025-03-31 10:53:09]:
I think it's.
我认为是的。


[2025-03-31 10:53:13]:
It's easier to use the exhibit photos.
使用展品照片更容易。


[2025-03-31 10:53:18]:
OK, I can try.
好的，我可以试试。


[2025-03-31 10:53:30]:
OK, thanks for taking the effort to understand what I suggested.
好的，感谢您花时间理解我的建议。


[2025-03-31 10:53:43]:
Then yes. And regarding that following up on.
然后是的。关于后续行动。


[2025-03-31 10:53:47]:
This point, so maybe you can even come up with a diagram illustrating the steps of the yellow.
这一点，所以也许你甚至可以想出一个图表来说明黄色的步骤。


[2025-03-31 10:53:56]:
Model that you use or the basically explaining your program a little bit more, maybe using a flow chart, using a diagram.
你使用的模型或基本上更多地解释你的程序，也许使用流程图，使用图表。


[2025-03-31 10:54:10]:
So that it's not completely a black box to us.
所以它对我们来说并不完全是一个黑匣子。


[2025-03-31 10:54:20]:
I remember I upload the.
我记得我上传了。


[2025-03-31 10:54:31]:
Oh.
哦。


[2025-03-31 10:54:47]:
Oh.
哦。


[2025-03-31 10:54:56]:
I remember I upload.
我记得我上传了。


[2025-03-31 10:55:03]:
Workflow to the.
工作流程到。


[2025-03-31 10:55:05]:
Why I can't find?
为什么我找不到？


[2025-03-31 10:55:26]:
You have
你有


[2025-03-31 10:55:31]:
Here is the workflow. Yes I remember that.
这是工作流程。有，我记得。


[2025-03-31 10:55:43]:
I also remember this one.
我还记得这个。


[2025-03-31 10:55:46]:
But I was talking about, maybe you can have another one for the algorithm only.
但我刚才说的是，也许你可以再为算法设置另一个。


[2025-03-31 10:55:54]:
OK.
还行。


[2025-03-31 10:55:56]:
But the training, the online calculation? And how does this happen in your code? Maybe you could have a very high level flow chart illustrating the algorithm?
但是训练，在线计算呢？这在你的代码中是如何发生的？也许您可以有一个非常高级的流程图来说明算法？


[2025-03-31 10:56:12]:
OK.
还行。


[2025-03-31 10:56:23]:
Which
哪


[2025-03-31 10:56:23]:
Which algorithm are you talking about? Are you talking about the algorithm that compares the shape of the outline with the shape? The shape of the outline of the human with the shape of the outline of the animal? I'm talking about everything, every code in this system.
您说的是哪种算法？您说的是比较轮廓形状和形状的算法吗？人的轮廓形状与动物的轮廓形状？我说的是这个系统中的所有内容、每一段代码。


[2025-03-31 10:56:41]:
OK, from the beginning to.
好的，从头到尾。


[2025-03-31 10:56:47]:
To the result.
结果。


[2025-03-31 10:56:53]:
So you just need a diagram kind of thing you mean showing.
所以你只需要一个图表，你想要展示的东西。


[2025-03-31 10:56:57]:
The different components and what's doing what? Yes, and the steps that your system follows.
不同的组件以及什么在做什么？是的，以及您的系统遵循的步骤。


[2025-03-31 10:57:08]:
OK, so that shouldn't take too long, I don't think, because, yeah, you've done the hard work of actually making it work, and you just need to draw some boxes and arrows. Yes, boxes and apples. Please don't spend too much time on it.
好的，所以这应该不会花太长时间，我认为，因为，是的，你已经完成了真正让它工作的艰苦工作，你只需要画一些方框和箭头。是的，盒子和苹果。请不要在上面花太多时间。


[2025-03-31 10:57:27]:
MMM.
嗯。


[2025-03-31 10:57:30]:
OK.
还行。


[2025-03-31 10:57:34]:
Oh, I'll do this on this week.
哦，我这周就来做这个。


[2025-03-31 10:57:37]:
Like
喜欢


[2025-03-31 10:57:43]:
OK, so do box and arrow diagnosis.
好的，做方框和箭头诊断吧。


[2025-03-31 10:57:53]:
And photos? Yes, take photos. Shall we arrange a time when you can come to the museum?
还有照片呢？是的，拍照。我们安排一个您可以来博物馆的时间吗？


[2025-03-31 10:58:00]:
So.
所以。


[2025-03-31 10:58:08]:
I'm available between the between Tuesday and Friday.
我在周二和周五之间有空。


[2025-03-31 10:58:14]:
Between Tuesday and Friday, yes. OK.
周二到周五之间，是的。还行。


[2025-03-31 10:58:21]:
Yeah, that's a good idea. So if we have a really nice database of photos from the actual exhibitions.
是的，这是个好主意。因此，如果我们有一个非常好的实际展览照片数据库。


[2025-03-31 10:58:30]:
I'm sorry, this is, I'm thinking ahead quite a bit. So this is, don't panic. You don't have it with what I say sounds complicated. I don't mean you should do it. I'm just getting excited. So I'm starting to think about all cool stuff that could be done. But so at the moment we're searching by outline, right? But these image analysis systems now can do all kinds of things.
对不起，这是，我提前考虑了很多。所以，不要惊慌。我说的听起来很复杂，你不会觉得。我不是说你应该这样做。我只是越来越兴奋。所以我开始考虑所有可以做的很酷的事情。但目前我们按 outline 进行搜索，对吧？但是这些图像分析系统现在可以做各种各样的事情。


[2025-03-31 10:58:56]:
Michael, I can analyze images in lots of different ways.
Michael，我可以用很多不同的方式分析图像。


[2025-03-31 10:59:03]:
So I'm thinking if we have a nice set of photos from the exes either we can start to think about other other things we can do apart from outline, Maybe color?
所以我在想，如果我们有一组来自前任的漂亮照片，我们是否可以开始考虑除了轮廓之外我们还能做的其他事情，也许是颜色？


[2025-03-31 10:59:16]:
Maybe even recognizing things like if I hold a tennis ball, it can recognize a ball and then find something in the exhibit that looks like a ball or that is a ball or what I mean. Or you can hold up a toy bird or something and say find me a bird that looks like, just hold it up and it will recognize that it's a bird and then find all the birds.
甚至可能识别出诸如我拿着网球之类的东西，它可以识别出一个球，然后在展品中找到看起来像球的东西，或者是球，或者我的意思是。或者你可以举起一只玩具鸟或其他东西，说给我找一只看起来像的鸟，只要把它举起来，它就会认出这是一只鸟，然后找到所有的鸟。


[2025-03-31 10:59:45]:
So I guess I'm thinking you've already you can feel easily substitute Yolo or probably Yolo does all this already. I know maybe you can already classify things based on what they actually are. So as well as doing the outline. So I think if we have a nice database if things when we exhibit exhibitions and opens up if we have time, it happens up the possibility to do that kind of search.
所以我想我在想你已经可以很容易地替代 Yolo，或者可能 Yolo 已经做了这一切。我知道也许您已经可以根据事物的实际含义对其进行分类。所以还要做大纲。所以我认为，如果我们有一个很好的数据库，当我们举办展览并开放时，如果我们有时间，就有可能进行这种搜索。


[2025-03-31 11:00:12]:
OK. And if you don't have time then maybe you want or and I can have a play with those kinds of ideas.
还行。如果你没有时间，那么也许你想要，或者我可以玩弄这些想法。


[2025-03-31 11:00:21]:
And Johan has semester 2 with us.
Johan 在我们这里度过了第 2 学期。


[2025-03-31 11:00:25]:
Yeah.
是的。


[2025-03-31 11:00:26]:
Yeah, unless whole year.
是的，除非一整年。


[2025-03-31 11:00:41]:
So I think for next week when she's not there is flow chart and take photos. That's enough I think. And maybe I guess you can keep tweaking like you said you're using Yolo version 8 or something, but now there's version 11. So maybe if it's possible to.
所以我想下周当她不在的时候，要有流程图和拍照。我想这就够了。也许我想你可以像你说的那样继续调整，你正在使用 Yolo 版本 8 或其他版本，但现在有版本 11。所以也许如果有可能的话。


[2025-03-31 11:00:41]:
OK, so I think for next week when she's not there is flow chart and take photos. That's enough I think. And maybe I guess you can keep tweaking like you said you're using Yolo version 8 or something, but now there's version 11, so maybe if it's possible to
好的，所以我想下周她不在的时候，要有流程图和拍照。我想这就够了。也许我想你可以像你说的那样继续调整，你正在使用 Yolo 版本 8 或其他版本，但现在有版本 11，所以也许如果有可能的话


[2025-03-31 11:00:58]:
Make version 11 work. That might be good if it opens up, if it works better or brings some new features.
使版本 11 正常工作。如果它打开，如果它运行得更好或带来一些新功能，那可能会很好。


[2025-03-31 11:01:07]:
OK.
还行。


[2025-03-31 11:01:10]:
But it seems like it's working well enough now anyway, so I guess that's probably the third thing on the list if there's time.
但无论如何，它现在似乎已经运行得足够好了，所以我想如果有时间的话，这可能是列表中的第三件事。


[2025-03-31 11:01:21]:
OK.
还行。


[2025-03-31 11:01:24]:
OK, I can try.
好的，我可以试试。


[2025-03-31 11:01:31]:
I'm really impressed with how well.
我对自己的表现印象深刻。


[2025-03-31 11:01:36]:
Works with that one thing actually, and that was doing that in real time as well. That's pretty cool.
实际上，它与一件事一起工作，而且也是实时完成的。这很酷。


[2025-03-31 11:01:50]:
And for taking photo I can do Thursday.
对于拍照，我可以做星期四。


[2025-03-31 11:01:54]:
Johan, OK first.
Johan，首先确定。


[2025-03-31 11:01:59]:
I should be in the museum all day so you can just come whenever and send me a message, OK? Send an e-mail, OK?
我应该整天都在博物馆里，这样你就可以随时来给我发信息，好吗？发送电子邮件，好吗？


[2025-03-31 11:02:19]:
OK
还行


[2025-03-31 11:02:20]:
Is there anything else we need to talk about?
还有什么需要我们谈的吗？


[2025-03-31 11:02:26]:
No, that's all my.
不，这就是我的全部。


[2025-03-31 11:02:30]:
Last week.
上个星期。


[2025-03-31 11:02:35]:
Nice work. That's great. Yeah, that's really good progress.
干得漂亮。真棒。是的，这确实是很好的进步。


[2025-03-31 11:02:41]:
Yep.
是的。


[2025-03-31 11:02:48]:
So our next meeting.
所以我们的下一次会议。


[2025-03-31 11:02:53]:
Is the same time next week, Yeah.
是下周的同一时间，是的。


[2025-03-31 11:02:59]:
Good for you, Johan. Thank you.
对你有好处，Johan。谢谢。


[2025-03-31 11:03:08]:
Say you nasturtium.
比如说金莲花。


[2025-03-31 11:03:12]:
OK, sounds good.
好的，听起来不错。


[2025-03-31 11:03:15]:
Have a good day, everyone. Have a good day. See you. Thank you. See you.
祝大家有美好的一天。祝你今天开心。再见。谢谢。再见。



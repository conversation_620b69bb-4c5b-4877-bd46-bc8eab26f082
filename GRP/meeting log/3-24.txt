[2025-03-24 10:37:11]:
After lunch, I can just go there. OK, great. I'll see you there. See you there.
午饭后，我就可以去那里了。好的，太好了。我们到时见。到时见。


[2025-03-24 10:37:18]:
Hi, sorry I didn't notice you were in the waiting room this whole time. No worries, I can't log in my UTS account just to know.
嗨，对不起，我没有注意到你一直在等候室里。不用担心，我无法登录我的 UTS 帐户以了解。


[2025-03-24 10:37:27]:
So I'm late actually.
所以我实际上迟到了。


[2025-03-24 10:37:31]:
How are you doing?
你好吗？


[2025-03-24 10:37:33]:
I also.
我也是。


[2025-03-24 10:37:35]:
I just can't login in my UTS account used online.
我就是无法登录我在网上使用的 UTS 帐户。


[2025-03-24 10:37:41]:
How are you today?
你今天感觉如何？


[2025-03-24 10:37:43]:
It's good
很好


[2025-03-24 10:37:45]:
It's good I have recovered.
我恢复了真好。


[2025-03-24 10:37:49]:
That's great, yeah, But I I'm still worn out because I did so much last week. I write a lot of useless coding.
太好了，是的，但我还是很疲惫，因为我上周做了很多事情。我写了很多无用的编码。


[2025-03-24 10:38:02]:
And now my laptop. That desk looks like a coding garbage mountain.
现在是我的笔记本电脑。那张桌子看起来像一座编码垃圾山。


[2025-03-24 10:38:13]:
I
我


[2025-03-24 10:38:14]:
I like the coding garbage mountain that sounds. Yeah, that sounds nice. Yeah. It's like poetry.
我喜欢听起来像是编码垃圾山。是的，听起来不错。是的。这就像诗歌。


[2025-03-24 10:38:23]:
Yeah, truth is like poetry.
是的，真理就像诗歌。


[2025-03-24 10:38:33]:
Let me open my Google documents.
让我打开我的 Google 文档。


[2025-03-24 10:38:44]:
Let me share on the table.
让我在桌子上分享。


[2025-03-24 10:38:55]:
I'll just quickly e-mail. I'm just on. Yeah. Did you? I don't see any review.
我只是快速发送电子邮件。我只是在。是的。你？我没有看到任何评论。


[2025-03-24 10:39:02]:
C&C, yeah, maybe. Oh, wait, sorry. No, Yeah, it's about.
C&C，是的，也许吧。哦，等等，对不起。不，是的，它是关于的。


[2025-03-24 10:39:09]:
Yeah, sorry. OK. That was a separate question about something else. Yeah.
是的，对不起。还行。那是关于其他事情的单独问题。是的。


[2025-03-24 10:39:22]:
Should I go now to go out now?
我现在应该去现在出去吗？


[2025-03-24 10:39:26]:
That could share your school
那可以共享你的学校


[2025-03-24 10:39:27]:
I might need to approve OK.
我可能需要批准 OK。


[2025-03-24 10:39:31]:
My gosh, I lost my mouse.
天哪，我把我的鼠标弄丢了。


[2025-03-24 10:39:58]:
OK.
还行。


[2025-03-24 10:40:02]:
And here is my update of my last week.
这是我上周的更新。


[2025-03-24 10:40:09]:
I completely finished the phantom with my model.
我用我的模型完全完成了幻影。


[2025-03-24 10:40:16]:
The original yellow model could like.
原来的黄色模型可能会喜欢。


[2025-03-24 10:40:20]:
Recognize it both of the class of the object and all lines and I modified it by removing the class part to focus on the human outlines and for the animal part I directly used the original Yulo model and.
识别它，对象的类和所有线条，我通过删除类部分来修改它，以专注于人类轮廓，对于动物部分，我直接使用了原始的 Yulo 模型和。


[2025-03-24 10:40:45]:
Is and used Cocoa database?
是 Cocoa 数据库，使用的是 Cocoa 数据库吗？


[2025-03-24 10:40:49]:
And I overall workflow on.
我总体工作流程是这样的。


[2025-03-24 10:40:55]:
Oh, I'm here.
哦，我在这里。


[2025-03-24 10:40:58]:
And I sorry, and I did.
我很抱歉，我确实做到了。


[2025-03-24 10:41:04]:
A basic UI design, but this design was created quickly just to present the prototype today and this isn't the Federation. Still need more refinement later and I can.
一个基本的 UI 设计，但这个设计是快速创建的，只是为了展示今天的原型，这不是 Federation。以后还需要更多的改进，我可以。


[2025-03-24 10:41:21]:
Share.
共享。


[2025-03-24 10:41:47]:
But what do you suggest? A basic teacher and?
但你有什么建议呢？一个基本的老师和？


[2025-03-24 10:41:54]:
Sorry, when the visitor click the start there will be a countdown for the visitor to post. It's like.
对不起，当访客点击开始时，访客将进入发布倒计时。就像。


[2025-03-24 10:42:05]:
Oops, start.
哎呀，开始。


[2025-03-24 10:42:11]:
It's not working.
它不起作用。


[2025-03-24 10:42:23]:
So we will take photo and we will contact them.
因此，我们将拍照并与他们联系。


[2025-03-24 10:42:29]:
And the.
和。


[2025-03-24 10:42:30]:
The visitor can choose retake or use. Use this photo and it will match later.
访客可以选择重拍或使用。使用此照片，稍后将进行匹配。


[2025-03-24 10:42:39]:
And show the animal picture and the March school and the visitor can choose try it again or save the result and send the e-mail to the visit.
并展示动物图片和三月学校，访客可以选择再试一次或保存结果并发送电子邮件给访客。


[2025-03-24 10:42:55]:
And
和


[2025-03-24 10:42:57]:
And this week I ran into a lot of challenges, especially when the fencing my model and writing the code, because I often ended up writing confused codes that nobody understand and include me so.
这周我遇到了很多挑战，尤其是在屏蔽我的模型和编写代码时，因为我经常写出令人困惑的代码，没有人理解我。


[2025-03-24 10:43:18]:
So I regularly ask a chat to BT to help and although sometimes ChatGPT.
所以我经常向 BT 请求聊天以提供帮助，尽管有时是 ChatGPT。


[2025-03-24 10:43:26]:
Give me correct code, but sometimes it's also private useful suggestion and help me correct my mistakes. So because of this I write an AI report.
给我正确的代码，但有时它也是私人的有用建议，帮助我纠正我的错误。因此，我写了一份 AI 报告。


[2025-03-24 10:43:45]:
I'm recording all my coding conversation with and including.
我正在记录我所有的编码对话。


[2025-03-24 10:43:50]:
What went wrong and how it was correct?
哪里出了问题，它是如何正确的？


[2025-03-24 10:43:58]:
And when I test my model.
当我测试我的模型时。


[2025-03-24 10:44:03]:
I'm facing two issues.
我面临两个问题。


[2025-03-24 10:44:06]:
The first adjust the red one, sorry. The 1st is the my model can only.
首先调整红色的，对不起。第一个是 my model only 。


[2025-03-24 10:44:17]:
Can only like recognize it one person at a time. So when I tested the model with the pictures.
一次只能认出一个人。所以当我用图片测试模型时。


[2025-03-24 10:44:27]:
Sorry when I tested the model with the pictures.
很抱歉，当我用图片测试模型时。


[2025-03-24 10:44:31]:
Multiple people it doesn't work so.
多人 它不起作用。


[2025-03-24 10:44:37]:
This is an important question in the museum setting. If the visitors want to take a group for sorry take a group photos, how should I handle that? Should I?
这是博物馆环境中的一个重要问题。如果访客想合群拍照，我该怎么办？我应该吗？


[2025-03-24 10:44:52]:
Add support for a multiple multiple photo people. Sorry.
添加对多个多个照片人物的支持。不好意思。


[2025-03-24 10:44:58]:
And.
和。


[2025-03-24 10:45:01]:
2nd about the database because I used the public available cocoa data database so it's not idea for our final expansion because this picture are not.
第二点是关于数据库的，因为我使用了公开可用的可可数据数据库，所以这不是我们最终扩展的想法，因为这张图片不是。


[2025-03-24 10:45:18]:
Sorry are not museum related.
抱歉，与博物馆无关。


[2025-03-24 10:45:22]:
So.
所以。


[2025-03-24 10:45:24]:
Last week I visited the museum and I am wondering if I can directly photograph the.
上周我参观了博物馆，我想知道我是否可以直接拍摄它。


[2025-03-24 10:45:34]:
The extension. I've used this picture in our matching system.
扩展。我已经在我们的匹配系统中使用了这张图片。


[2025-03-24 10:45:44]:
Like.
喜欢。


[2025-03-24 10:45:46]:
Take photo of the animals in museum and then I can design the interaction around idea like.
给博物馆里的动物拍照，然后我可以围绕这个想法设计互动。


[2025-03-24 10:45:57]:
Which museum expand much you best and maybe even include the location of the each animals. But this just an idea because I'm a little bit concerned about the visitor may take make a unusual post and.
哪个博物馆最能扩展您，甚至可能包括每种动物的位置。但这只是一个想法，因为我有点担心访问者可能会发表不寻常的帖子和。


[2025-03-24 10:46:23]:
Then find out too much with any animals.
然后发现任何动物都太多了。


[2025-03-24 10:46:28]:
So.
所以。


[2025-03-24 10:46:30]:
That's all my last week.
这就是我的最后一周。


[2025-03-24 10:46:41]:
Should I call first? I think this is great progress, well done and the interface looks nice and.
我应该先打电话吗？我认为这是很大的进步，做得很好，界面看起来不错。


[2025-03-24 10:46:52]:
For your question, single person or multiple person detection, I would say how about let's focus on doing the single person one really good before we worry about.
对于您的问题，单人或多人检测，我想说的是，在我们担心之前，让我们专注于真正做好单人检测怎么样。


[2025-03-24 10:47:08]:
In a detecting multiple person matching with multiple person? Because I think one improvement that you can do for your current prototype is that I can't really see the links between the gecko and your post. I mean generating the geckos very interesting and fun at last, but if you could somehow find a way to I guess visualize the connection.
在检测多个人匹配与多个人匹配时？因为我认为你可以对当前的原型做的一个改进是，我真的看不到 gecko 和你的帖子之间的联系。我的意思是最后生成壁虎非常有趣和有趣，但如果你能以某种方式找到一种方法来可视化这种联系。


[2025-03-24 10:47:35]:
Between the post and the matched will be good. Would be good for user experience.
在帖子和匹配之间会很好。对用户体验有好处。


[2025-03-24 10:47:44]:
And I also really like the idea of matching with the exceeded because that makes the connection of the current things on this play so.
我也非常喜欢与 exceeded 匹配的想法，因为这使得这部剧中当前事物的联系如此。


[2025-03-24 10:47:59]:
I think that's a good idea.
我认为这是个好主意。


[2025-03-24 10:48:02]:
And is there a way to because your question was.
有没有办法，因为你的问题是。


[2025-03-24 10:48:08]:
What if they post and there's no exhibit that can match with the post right? Is there a way that you could possibly find the best match one it doesn't have to be.
如果他们发帖了，但没有可以与帖子匹配的展品怎么办，对吧？有没有一种方法可以让您找到最匹配的一个，它不必是。


[2025-03-24 10:48:24]:
Very similar, but.
非常相似，但是。


[2025-03-24 10:48:27]:
Would it be possible to just?
有可能只是吗？


[2025-03-24 10:48:29]:
Show the closest.
显示最近的。


[2025-03-24 10:48:33]:
The closest one.
最接近的那个。


[2025-03-24 10:48:37]:
OK.
还行。


[2025-03-24 10:48:40]:
I don't know if it fits with your algorithm, but it's just a suggestion. And also anyways, that's for the next step, right? Because currently maybe we could focus on making the.
我不知道它是否符合您的算法，但这只是一个建议。而且，无论如何，这是下一步，对吧？因为目前也许我们可以专注于制作。


[2025-03-24 10:48:57]:
My first suggestion.
我的第一个建议。


[2025-03-24 10:48:59]:
Improve the results presentation.
改进结果显示。


[2025-03-24 10:49:05]:
OK, so that's my thought.
好的，这就是我的想法。


[2025-03-24 10:49:09]:
Over to you, Andrew.
交给你了，安德鲁。


[2025-03-24 10:49:12]:
I see you could also said it's OK, even if it's not 100%, it can be 20 or 30%. But I guess if you can do what you are suggesting, you are like showing is the outline of you and he's the outline of the gecko and then you can kind of hopefully they'll be able to see that connection or whether it's the color or other things. Yeah.
我看到你也可以说没关系，即使不是 100%，也可以是 20% 或 30%。但我想，如果你能按照你的建议去做，你就会展示你的轮廓，而他是壁虎的轮廓，然后你可以希望他们能够看到这种联系，或者是颜色还是其他东西。是的。


[2025-03-24 10:49:37]:
I mean, it doesn't matter. Sorry Android, I interrupted you again. Oh, I just wanted to say, even if it's not really similar, but at least people can see. OK, You, you've, you've done this.
我的意思是，这没关系。对不起，Android，我又打断你了。哦，我只是想说，即使它不是真的相似，但至少人们可以看到。好的，你，你已经，你已经做到了。


[2025-03-24 10:49:42]:
And even it doesn't matter. Sorry Android, I interrupted you again. Oh, I just wanted to say even if it's not really similar but at least people can see OK you
即使这并不重要。对不起，Android，我又打断你了。哦，我只是想说，即使它不是真的相似，但至少人们可以看到 OK you


[2025-03-24 10:49:51]:
I if I agree or I don't agree, that's my opinion, right so.
我同意或不同意，这就是我的观点，对。


[2025-03-24 10:49:57]:
Exactly, and you could say probably drawing a dotted line or something around the outline of the person.
没错，你可以说可能在人的轮廓周围画一条虚线或其他东西。


[2025-03-24 10:50:08]:
And then that one around the gecko. And then I guess maybe you could even pop them both out somehow and show the two outlines superimposed. I don't know, There's lots of different ways you could do it. But a good first step anyway, I guess would be to somehow highlight why it is that they're.
然后是那个围绕壁虎的那个。然后我猜也许你甚至可以以某种方式将它们都弹出来，并显示叠加的两个轮廓。我不知道，你可以用很多不同的方法来实现。但无论如何，一个好的第一步，我想是以某种方式强调他们为什么会这样。


[2025-03-24 10:50:27]:
A close, somewhat close match.
一场势均力敌的比赛。


[2025-03-24 10:50:30]:
And I definitely agree about using the photos from the exhibits and things. I think that's a really good idea so that people can actually see around the room the geckos or whatever other things there are that are in the exhibit and directly kind of.
我绝对同意使用展品和其他东西的照片。我认为这是一个非常好的主意，这样人们就可以真正看到房间周围的壁虎或展览中的其他任何东西，并且直接看到。


[2025-03-24 10:50:45]:
Connect with it as opposed to some random, hopefully some, yes, some random thing from the.
与它连接，而不是一些随机的，希望是一些，是的，一些随机的东西。


[2025-03-24 10:50:55]:
The whole digitized collection.
整个数字化馆藏。


[2025-03-24 10:51:01]:
I'm kinda sorta checked out just the mock up that you showed like a demo that you showed before with the gecko. Is that actually doing a match between your photo and something in the database or is that just a mock up to show us how it will work?
我有点检查了你展示的模型，就像你之前用 gecko 展示的演示一样。这实际上是在你的照片和数据库中的东西之间进行匹配，还是只是一个模型，向我们展示它是如何工作的？


[2025-03-24 10:51:18]:
It's just like.
就像。


[2025-03-24 10:51:21]:
A design for the present prototype today, actually my system can put show the mask on the picture and can match five different animals. But actually I'm not sure how to like link, link that to my interaction design. I I'm I just find a way.
今天原型的设计，实际上我的系统可以将面具显示在图片上，并且可以匹配五种不同的动物。但实际上我不确定如何喜欢链接，将其链接到我的交互设计。我，我是，我只是找到一种方法。


[2025-03-24 10:51:49]:
I already finished the system and can show the mask the green mask.
我已经完成了系统，可以向面具显示绿色面具。


[2025-03-24 10:51:58]:
Online for the result and I
在线查看结果和我


[2025-03-24 10:52:02]:
Like design, the interaction design, the UI design, but I don't know how to link that too.
比如设计、交互设计、UI 设计，但我也不知道如何将它们联系起来。


[2025-03-24 10:52:11]:
Yeah, sure. That's a big job. I was just gonna ask is it possible to see in real time? Yeah, no, you need, you need take photo and do you post and take like the workflow you need to take photo not in time.
当然。这是一项艰巨的工作。我只是想问，是否可以实时查看？是的，不，你需要，你需要拍照，你发布和拍摄就像你需要拍照的工作流程一样，而不是及时。


[2025-03-24 10:52:34]:
So much with the picture that's taken but if you change posts like on the flight and can't, you need to take this the picture again.
拍摄的照片就这么多了，但如果你像在飞机上一样更换帖子而无法更改，则需要再次拍摄这张照片。


[2025-03-24 10:52:46]:
Right, because it I was thinking it could be, it would be nice to be able to see so I can see the outline and I can try and make the shape of the cow or whatever and then somehow take the photo. Although I don't know how I do that without changing my post but.
是的，因为我认为它可能，如果能够看到，这样我就能看到轮廓，我可以尝试制作奶牛的形状或其他任何东西，然后以某种方式拍摄照片，那就太好了。虽然我不知道如何在不改变我的帖子的情况下做到这一点，但是。


[2025-03-24 10:53:04]:
It would be nice to be able to see that. Or I guess the other way to do that is to just take the photo and then show me the outline and then you can say do you want to take it again?
如果能看到这一点就好了。或者我想另一种方法是拍照片，然后给我看轮廓，然后你可以说你想再拍一次吗？


[2025-03-24 10:53:17]:
But OK, yeah, now I'm saying that I'm thinking.
但是，好吧，是的，现在我说我在思考。


[2025-03-24 10:53:24]:
I might as well see my next animal and then I can always take it again after that, can't I? So maybe my idea isn't so good.
我还不如看看我的下一只动物，然后我总是可以再吃一次，不是吗？所以也许我的想法不是那么好。


[2025-03-24 10:53:35]:
OK.
还行。


[2025-03-24 10:53:38]:
I don't know what do you think. I think it's a good idea, but I don't know how high it will be for you how to implement.
我不知道你怎么想。我认为这是个好主意，但我不知道如何实现对你来说会有多高。


[2025-03-24 10:53:47]:
I can try question. If it's more dynamic then it's more smart. This is what we tend to think when we see an interface so.
我可以尝试提问。如果它更有活力，那么它就会更聪明。这就是我们看到 SO 界面时倾向于思考的。


[2025-03-24 10:54:01]:
I mean, if you can do that would be great, but I know I don't know how much complexity you will add to your work, Johan.
我的意思是，如果你能做到这一点就太好了，但我知道我不知道你会给你的工作增加多少复杂性，Johan。


[2025-03-24 10:54:12]:
I can try.
我可以试试。


[2025-03-24 10:54:15]:
So I think the with things like this.
所以我认为像这样的事情。


[2025-03-24 10:54:19]:
We can suggest lots of things, but you have the power to kind of go, I can't do that or it's too much work. So I think for the main of this next phase, I think if you can just show the outline somehow on the top of the photos.
我们可以提出很多建议，但你有能力去做，我不能这样做，否则工作量太大。所以我认为对于下一阶段的主要内容，我认为如果你能以某种方式在照片的顶部显示轮廓。


[2025-03-24 10:54:41]:
That would be a really, that's the main thing. And then if you can do some kind of real time showing me the outline while I'm posing, that would be very nice to have. But if it's impossible in the time we have, then it doesn't matter.
那真的，这是主要的事情。然后，如果你能在我摆姿势时做一些实时的动作，向我展示轮廓，那就太好了。但是，如果在我们拥有的时间里是不可能的，那也没关系。


[2025-03-24 10:54:59]:
It's a nice to have, not a must have.
这是一件好事，而不是必须的。


[2025-03-24 10:55:03]:
OK, maybe I can a just type after the take photo. Like I take the visitor take the photo and show the online and the reader can choose take it again or?
好吧，也许我可以在拍照后打字。就像我带访客拍照片并在线展示一样，读者可以选择再拍一次还是？


[2025-03-24 10:55:20]:
I'll go to the match system.
我会去比赛系统。


[2025-03-24 10:55:23]:
Yeah, I guess it's easier for me.
是的，我想这对我来说更容易。


[2025-03-24 10:55:27]:
Yeah, that sounds fine.
是的，听起来不错。


[2025-03-24 10:55:30]:
Do do what's possible?
做可能的事吗？


[2025-03-24 10:55:33]:
And then if we have time, we can try the dynamic idea exactly. So we can have a big pile of things that might be nice, that doesn't matter. We do them, we might do them, or we'll do the ones that we have time to get to. But at least it looks like we're getting pretty close to having like a minimum.
然后如果我们有时间，我们可以准确地尝试动态的想法。所以我们可以有一大堆可能很好的东西，这并不重要。我们做它们，我们可能会做它们，或者我们会做我们有时间去做的那些。但至少看起来我们已经非常接近最低限度了。


[2025-03-24 10:55:55]:
Festival Orlando, right and I was watching already got it. So it's almost.
Festival Orlando，对，我正在看已经明白了。所以差不多。


[2025-03-24 10:56:00]:
You don't get all the most start testing that I think, so maybe one more round of tweaking and.
你不会像我认为的那样得到最多的开始测试，所以也许再进行一轮调整。


[2025-03-24 10:56:08]:
Creating more coding garbage mountain.
创造更多的编码垃圾山。


[2025-03-24 10:56:14]:
We can get yes, my cabbage mountain.
我们可以得到 Yes， my cabbage mountain。


[2025-03-24 10:56:19]:
My cabbage mountain here.
我的卷心菜山在这里。


[2025-03-24 10:56:25]:
I was gonna actually suggest as well. I don't have much experience using using it but.
我实际上也打算提出建议。我没有太多使用它的经验，但是。


[2025-03-24 10:56:31]:
People. I think Claude dot AI might be quite good for code.
人。我认为 Claude dot AI 可能非常适合代码。


[2025-03-24 10:56:38]:
So if you want to, if you're having trouble with GPT and then it's not giving you very good code, you could try using quotes. If it does any better, Sorry, close. It's in the chat. I put it cloud dot AI.
所以如果你愿意，如果你在使用 GPT 时遇到问题，然后它没有给你很好的代码，你可以尝试使用引号。如果情况有所改善，对不起，关闭。它在聊天中。我把它放在 cloud dot AI 上。


[2025-03-24 10:56:53]:
How many times? Thank you for coding. It's better than ChatGPT. Oh.
多少次？感谢您的编码。它比 ChatGPT 好。哦。


[2025-03-24 10:57:03]:
OK. Thank you.
好的，谢谢。


[2025-03-24 10:57:06]:
And it's, yeah, it's free to some extent, but I'm not sure what the limits are. Yeah.
是的，它在某种程度上是免费的，但我不确定限制是什么。是的。


[2025-03-24 10:57:15]:
The free plan only allows you to send a limited amount of messages per hour or something, or for several hours.
免费计划只允许您每小时或更长时间发送有限数量的消息，或几个小时。


[2025-03-24 10:57:28]:
Ten shots, you smell like 10 charts.
十杯，你闻起来像 10 张图表。


[2025-03-24 10:57:30]:
It's not enough.
这还不够。


[2025-03-24 10:57:32]:
So usually you will see people send their first prompt really long.
所以通常你会看到人们发送他们的第一个提示很长。


[2025-03-24 10:57:38]:
Because that's like one message, OK.
因为那就像一条信息，好的。


[2025-03-24 10:57:46]:
It's 4 hours. Well Yuka has a lot of experience with clock. I don't know how much it costs me though. I mean if it only costs $20.00 for to use it for a month or something, maybe it's worth it.
这是 4 小时。嗯，Yuka 在时钟方面有很多经验。不过我不知道这要花多少钱。我的意思是，如果使用它一个月左右只需要 20.00 美元左右，也许它是值得的。


[2025-03-24 10:57:59]:
Rather than waiting for four hours to do the next thing. But I think it's 2820 USD so that's like 30. I don't know.
而不是等四个小时再做下一件事。但我认为是 2820 美元，所以大概是 30 美元。我不知道。


[2025-03-24 10:58:14]:
It's it should be roughly the same as chargeability.
它应该与 chargeability 大致相同。


[2025-03-24 10:58:20]:
MMM, I'll try anyway. Worth a try, see what you see if it's helpful. If it's not, you know you're making really good progress already with what you're doing so
嗯，我还是试试。值得一试，看看你看到什么是否有帮助。如果不是，您就知道您正在做的事情已经取得了非常好的进展


[2025-03-24 10:58:28]:
I'll try anyway. Worth a try, see what you see if it's helpful. If it's not, you know you're making really good progress already with what you're doing so.
无论如何我都会尝试。值得一试，看看你看到什么是否有帮助。如果不是，您就知道您正在做的事情已经取得了非常好的进展。


[2025-03-24 10:58:29]:
Know it's obviously working pretty well.
要知道它显然运行得很好。


[2025-03-24 10:58:32]:
Yeah.
是的。


[2025-03-24 10:58:37]:
OK, Oh, by the way, I actually pay for the collab collab and because I don't have the unit on my laptop, so I.
好吧，哦，顺便说一句，我实际上为 collab 合作付费，因为我的笔记本电脑上没有这个装置，所以我。


[2025-03-24 10:58:52]:
Because I eyesight is 25 or $30 last week but because I thought it's USD but when I pay I found it AUD so it's actually $50.
因为我上周的视力是 25 或 30 美元，但因为我以为是美元，但当我付款时我发现它是澳元，所以实际上是 50 美元。


[2025-03-24 10:59:16]:
So what's $50? Is that collab for the Model 3?
那么什么是 50 美元？这是 Model 3 的合作吗？


[2025-03-24 10:59:25]:
The collab probe. Yeah, Collab Pro I signed last week.
协作探针。是的，我上周签约了 Collab Pro。


[2025-03-24 10:59:33]:
OK, is it a platform where your hand does the coding? Oh yeah, may may the model training. I make the model training, Yes, I use the.
好的，它是一个由您的手进行编码的平台吗？哦，对了，可以进行模型训练。我制作模型训练，是的，我使用。


[2025-03-24 10:59:48]:
That's what.
就是这样。


[2025-03-24 10:59:53]:
Like
喜欢


[2025-03-24 10:59:54]:
Like.
喜欢。


[2025-03-24 10:59:55]:
If I used to throw version, I just can't use the CPU.
如果我以前抛出版本，我就是不能使用 CPU。


[2025-03-24 11:00:03]:
So I pay for it, these things like, yeah, it's easy to suddenly spend a lot of money, isn't it? But he's actually just a subscription these days. Yeah, but he's just a $50.
所以我为此付费，这些事情，比如，是的，很容易突然花很多钱，不是吗？但实际上，他现在只是一个订阅。是的，但他只有 50 美元。


[2025-03-24 11:00:21]:
Yeah, well, it's.
是的，嗯，是的。


[2025-03-24 11:00:25]:
OK, Know it's whatever you're doing, it's going well so.
好的，知道无论你在做什么，它都会进展顺利。


[2025-03-24 11:00:32]:
If Cord AI with the free version else get you around some problems and that's great, but otherwise I'd say yeah, just keep going.
如果 Cord AI 与免费版本一起可以帮你解决一些问题，那很好，但除此之外我会说是的，继续前进。


[2025-03-24 11:00:47]:
Was there anything else you wanted to ask or anything we can help with?
您还有什么想问的，或者我们能帮忙的吗？


[2025-03-24 11:00:53]:
No, that's all. Actually, I that's all my challenges for last week.
不，就这样。实际上，这就是我上周的所有挑战。


[2025-03-24 11:01:06]:
All right.
好吧。


[2025-03-24 11:01:08]:
And so next meeting, same time next week, yeah.
所以下次会议，下周的同一时间，是的。


[2025-03-24 11:01:17]:
OK.
还行。


[2025-03-24 11:01:23]:
Unless, alright, well.
除非，好吧，好吧。


[2025-03-24 11:01:26]:
Is there anything else you want? Yeah, Thank you. No, I just wanted to say well done. Thank you. Progress every week.
你还想要什么吗？是的，谢谢。不，我只是想说干得好。谢谢。每周都有进步。


[2025-03-24 11:01:35]:
Yeah, that's very impressive. Yeah, pretty cool. So it's a nice work and keep it up and we'll see you next Monday, say next Monday. See you. Good luck. Thanks. OK, see you again. Bye. See you. See you in the afternoon, Andrew. Yes, see you there.
是的，这非常令人印象深刻。是的，很酷。所以，这是一项很好的工作，请继续努力，我们下周一见，比如下周一。再见。祝你好运。谢谢。好的，再见。再见。再见。下午见，安德鲁。是的，到时见。



[2025-05-05 10:31:22]:
Hi sorry my cat keeps pouring so I want to feed her
嗨，对不起，我的猫一直在倒，所以我想喂她


[2025-05-05 10:31:28]:
Sorry, my cat keeps purring so I want to feed her.
对不起，我的猫一直咕噜咕噜地叫，所以我想喂她。


[2025-05-05 10:31:28]:
That's all right.
没关系。


[2025-05-05 10:31:31]:
How are you?
你好吗？


[2025-05-05 10:31:34]:
It's good, but I got the cold again. No, Yeah.
这很好，但我又感冒了。不，是的。


[2025-05-05 10:31:44]:
So just.
所以只是。


[2025-05-05 10:31:45]:
Two of us today.
我们今天有两个人。


[2025-05-05 10:32:09]:
I got the code again.
我又拿到了代码。


[2025-05-05 10:32:15]:
Bad news.
坏消息。


[2025-05-05 10:32:19]:
Maybe buy the vitamin C from chemist the tablet.
也许从药剂师那里购买维生素 C。


[2025-05-05 10:32:25]:
Yeah.
是的。


[2025-05-05 10:32:37]:
Is there any update you want to show us today?
您今天有什么更新想给我们看的吗？


[2025-05-05 10:32:41]:
Yes, I mean.
是的，我是说。


[2025-05-05 10:32:46]:
Sure.
确定。


[2025-05-05 10:32:56]:
MMM
嗯


[2025-05-05 10:32:57]:
Regarding sorry, regarding the matching of the allies, I found the problems but I haven't fixed it yet.
关于抱歉，关于盟友的匹配，我发现了问题，但我还没有解决。


[2025-05-05 10:33:10]:
I've basically been debugging this week and try to fix it in different ways and I change the online with colors at the UN side.
这周我基本上一直在调试并尝试以不同的方式修复它，我在联合国方面用颜色在线更改了它。


[2025-05-05 10:33:25]:
I found the problem that if I change the size of the window it will be much, but if I.
我发现了一个问题，如果我改变窗口的大小，它会很多，但是如果我。


[2025-05-05 10:33:37]:
Like the?
喜欢吗？


[2025-05-05 10:33:42]:
Yeah, that looks promising. So I mean like without the scaling, without moving the window, it seems like it's pretty good at first place. I think so, Yep. What is this?
是的，这看起来很有希望。所以我的意思是，没有缩放，没有移动窗口，它一开始看起来就相当不错。我想是的，是的。这是怎麽？


[2025-05-05 10:33:57]:
What do you actually they interface is running in the browser like what is that actually running in like what's the code, the coding environment?
你实际上他们的界面在浏览器中运行什么，比如实际运行什么，比如代码、编码环境是什么？


[2025-05-05 10:34:10]:
Sorry, pardon.
对不起，对不起。


[2025-05-05 10:34:12]:
I'm just wondering the code that it's running there in the browser. What language or framework is the person?
我只是想知道它在浏览器中运行的代码。这个人是什么语言或框架？


[2025-05-05 10:34:25]:
Yeah.
是的。


[2025-05-05 10:34:26]:
It looks like it's a local host too.
看起来它也是一个本地主机。


[2025-05-05 10:34:30]:
Yes, it's a local host and I argued about the. I changed about the function twice.
是的，这是一个本地主机，我为此争论了一下。我对功能进行了两次更改。


[2025-05-05 10:34:42]:
It might, I mean often, I don't know, I've never, I haven't used Python for web development stuff, but I mean, I know for other frameworks there's usually like an event that's triggered when someone resizes the window and you can kind of write a bit of code to respond to that event. OK, just in there whenever that event happens, you've got to somehow just.
可能，我的意思是，我不知道，我从来没有，我没有用 Python 进行 Web 开发，但我的意思是，我知道对于其他框架，通常有一个事件，当有人调整窗口大小时会触发一个事件，你可以编写一些代码来响应该事件。好吧，只要那个事件发生，你就得以某种方式。


[2025-05-05 10:34:43]:
Yeah, it might
是的，它可能会


[2025-05-05 10:35:06]:
Recenter or rescale the outline. That will be my guess, but it's not.
重新居中或重新缩放轮廓。那将是我的猜测，但事实并非如此。


[2025-05-05 10:35:14]:
Yeah. It's probably not a very helpful guess, but you if there is an event that you can find that's like that might be a pathway to solving it. Yeah.
是的。这可能不是一个非常有用的猜测，但如果你能找到一个类似的事件，这可能是解决它的途径。是的。


[2025-05-05 10:35:28]:
Like something like responsive design?
喜欢响应式设计之类的东西吗？


[2025-05-05 10:35:35]:
Yeah, I don't know. I know I'm processing and open frameworks and some of those other.
是的，我不知道。我知道我正在处理和开放框架以及其他一些框架。


[2025-05-05 10:35:42]:
Coding frameworks, yeah, they have events and there's a bank for when someone moves their mouse. There's an event for when someone clicks on something. There's the back when someone resources the window.
编码框架，是的，它们有事件，并且有一个库用于当有人移动鼠标。当有人点击某物时，有一个事件。当有人为窗口提供资源时，就会出现。


[2025-05-05 10:35:51]:
And you can just write code to be triggered whenever that event happens. So but so. I think that's probably why you need to look, but I really have no idea how.
而且，你可以编写代码，以便在该事件发生时触发。所以，但如此。我想这可能就是你需要看的原因，但我真的不知道怎么做。


[2025-05-05 10:36:03]:
Python sort of web framework or whatever.
Python 之类的 Web 框架之类的。


[2025-05-05 10:36:06]:
Operates. It could have a completely different way of doing things. I have feeling maybe it's a like a function issue because.
经营。它可以有一种完全不同的做事方式。我感觉这可能是一个功能问题，因为。


[2025-05-05 10:36:16]:
There are actually 2 layers on the page. The first page is the video and I made the other page for the segment. You said like a like a camera and it's not locked. When I changed the side with the window, maybe the video size is changed but the segment cover did not did not.
页面上实际上有 2 层。第一页是视频，我为该段落制作了另一页。你说就像一个摄像头，它没有被锁定。当我用窗口改变一侧时，也许视频大小发生了变化，但片段封面没有。


[2025-05-05 10:36:43]:
Sorry
不好意思


[2025-05-05 10:36:44]:
Sorry.
不好意思。


[2025-05-05 10:36:48]:
I think I would say.
我想我会说。


[2025-05-05 10:36:53]:
In the.
在。


[2025-05-05 10:36:54]:
Whatever bit of code that runs when someone resizes the window, you just need to scale probably the one line of code to scale the outline to be proportional to the new window size.
无论在有人调整窗口大小时运行什么代码，您都只需要缩放一行代码，即可将轮廓缩放为与新窗口大小成比例。


[2025-05-05 10:37:08]:
I can try.
我可以试试。


[2025-05-05 10:37:11]:
Not a lot of code to write, but it's just finding the spot to write it and the exact right the exact line to make it all go line up. But anyway, so I can so I can see you're pretty close actually to me. OK, thank you. I can try my best. Yeah, and.
不需要编写很多代码，但只需找到编写它的位置和确切的正确行，使它们对齐。但无论如何，这样我就可以这样我看到你实际上离我很近。好的，谢谢你。我可以尽力而为。是的，而且。


[2025-05-05 10:37:36]:
Oh
哦


[2025-05-05 10:37:37]:
Yeah, and for the you're fantastic. I like to quickly walk through you through my user test.
是的，而且 You Are Fantastic。我想快速介绍一下我的用户测试。


[2025-05-05 10:37:47]:
I.
我。


[2025-05-05 10:37:50]:
For the.
对于。


[2025-05-05 10:37:55]:
Yup, for the data collection method first.
是的，首先是数据收集方法。


[2025-05-05 10:38:01]:
Observational Study.
观察性研究。


[2025-05-05 10:38:04]:
I'll try how long you interacted with the system and how.
我将尝试您与系统交互的时间和方式。


[2025-05-05 10:38:11]:
Engaged they are, and.
他们参与其中，而且。


[2025-05-05 10:38:15]:
Whether they come back to use it again and the second server is?
他们是否会再次回来使用它，而第二个服务器是？


[2025-05-05 10:38:23]:
Service.
服务。


[2025-05-05 10:38:28]:
Yes, this will help me understand the users overall experience and how educational they found this system system and how easy it was to use.
是的，这将帮助我了解用户的整体体验以及他们发现该系统系统的教育程度以及它的易用性。


[2025-05-05 10:38:43]:
And is the interviews.
而且是采访。


[2025-05-05 10:38:50]:
I would conduct an interview before they use and follow up interviews after users try this system and to get a deeper feedback and the ideas.
我会在他们使用之前进行访谈，并在用户试用此系统后进行后续访谈，以获得更深入的反馈和想法。


[2025-05-05 10:39:06]:
For the improvement.
为了改进。


[2025-05-05 10:39:10]:
And.
和。


[2025-05-05 10:39:12]:
For the user testing.
对于用户测试。


[2025-05-05 10:39:16]:
There are three main parts. The first usability testing. I want to see if users can.
有三个主要部分。第一次可用性测试。我想看看用户是否可以。


[2025-05-05 10:39:25]:
Can interact with the system naturally and without confusion and 2nd user experience evaluation. I'll use a like a standard usability scale to make sure how.
可以自然地与系统交互，而不会产生混淆和第二次用户体验评估。我将使用一个类似标准的可用性量表来确保如何作。


[2025-05-05 10:39:46]:
Euphoria and 3rd AB testing to compare traditional experience with the interactive system to see which one helps users to learn and stay engaged more effective and my plan is that if I have to.
Euphoria 和 3rd AB 测试将传统体验与交互式系统进行比较，看看哪一个可以帮助用户更有效地学习和保持参与，我的计划是，如果必须的话。


[2025-05-05 10:40:10]:
Write a write to the AC next term I can finish the first draft over the holidays, the two-month holidays. So I can complete the user testing in these two weeks.
下学期写一封信给 AC 我可以在假期，也就是两个月的假期里完成初稿。所以我可以在这两周内完成用户测试。


[2025-05-05 10:40:29]:
Is this available?
这有空吗？


[2025-05-05 10:40:40]:
So just let me kind of think. So basically what we're trying to figure out is.
所以让我思考一下。所以基本上我们试图弄清楚的是。


[2025-05-05 10:40:51]:
How? OK, so how engaging do they, how easy do they find us to use? How engaging is it and what impact does it have on?
如何？好的，那么他们有多吸引人，他们发现我们有多容易使用呢？它的吸引力如何，它有什么影响？


[2025-05-05 10:41:01]:
Their overall experience of the exhibition.
他们对展会的整体体验。


[2025-05-05 10:41:04]:
Is that? Yeah, three main things, Yes, Yeah.
那是？是的，三个主要的事情，是的，是的。


[2025-05-05 10:41:13]:
Could you show me the questionnaire again?
您能再给我看一遍问卷吗？


[2025-05-05 10:41:19]:
Interview or the survey? Maybe the survey to start with. OK.
采访还是调查？也许从调查开始。还行。


[2025-05-05 10:41:30]:
It's just a draft.
这只是一份草稿。


[2025-05-05 10:41:33]:
Yeah.
是的。


[2025-05-05 10:41:41]:
So this is only gonna be people that are 18 and over, yes.
所以这只会是 18 岁及以上的人，是的。


[2025-05-05 10:41:52]:
Is there a usability scale? The standardized 1?
有可用性量表吗？标准化的 1？


[2025-05-05 10:41:58]:
And
和


[2025-05-05 10:42:03]:
As you mentioned, you're going to use a standardized question.
正如你提到的，你将使用一个标准化的问题。


[2025-05-05 10:42:09]:
Is that one? Yeah. And about technological performance, this is 1.
是那个吗？是的。关于技术性能，这是 1。


[2025-05-05 10:42:18]:
I'm not sure if it's enough.
我不确定这是否足够。


[2025-05-05 10:42:23]:
Because it's just the 6th question.
因为这只是第 6 个问题。


[2025-05-05 10:42:28]:
Did you come out with these questions or did you search online?
您是提出这些问题还是在网上搜索的？


[2025-05-05 10:42:35]:
I search search it online.
我在网上搜索。


[2025-05-05 10:42:40]:
Yeah.
是的。


[2025-05-05 10:42:43]:
Whoops.
哎 呦。


[2025-05-05 10:42:47]:
So usually when we conduct usability or user experience testing, we use standardized or validated questionnaires and they are usually published by other researchers. So basically they will conduct all sorts of analysis like factor analysis to make sure the items or the questions in the same questionnaires.
因此，通常当我们进行可用性或用户体验测试时，我们会使用标准化或经过验证的问卷，这些问卷通常由其他研究人员发布。所以基本上他们会进行各种分析，比如因子分析，以确保项目或问题在相同的问卷中。


[2025-05-05 10:43:12]:
Basically work together.
基本上是一起工作。


[2025-05-05 10:43:17]:
And we will usually go with those questionnaires.
我们通常会带着这些问卷。


[2025-05-05 10:43:22]:
1st so I'm not sure if these questionnaires you found online.
第一，所以我不确定你是否在网上找到了这些问卷。


[2025-05-05 10:43:29]:
Were standardized
是标准化的


[2025-05-05 10:43:30]:
Were standardized.
被标准化。


[2025-05-05 10:43:34]:
I do have one disability.
我确实有一种残疾。


[2025-05-05 10:43:37]:
Scale that I know is a standardized.
我知道的规模是标准化的。


[2025-05-05 10:43:40]:
Questionnaire that I can share with you. Yeah, thank you. Yeah, I will share with you.
我可以与您分享的问卷。是的，谢谢。是的，我会与你分享。


[2025-05-05 10:43:48]:
OK.
还行。


[2025-05-05 10:43:51]:
Yeah, that makes sense. If you're testing usability, it makes sense to use the standard questionnaire and then the things that are specific to this particular.
是的，这是有道理的。如果您正在测试可用性，那么使用标准问卷，然后使用特定于此特定内容的内容是有意义的。


[2025-05-05 10:44:03]:
Application, then we can use our own questions for that, yeah.
申请，那么我们可以使用我们自己的问题来解决这个问题，是的。


[2025-05-05 10:44:10]:
So accuracy of recognition.
所以识别的准确性。


[2025-05-05 10:44:16]:
So I have system stability. I can come into survey like you share with me the survey and the survey myself.
所以我有系统稳定性。我可以像您与我分享调查和我本人一样参与调查。


[2025-05-05 10:44:39]:
Yeah, 1
是的，1


[2025-05-05 10:44:39]:
One thing to think about a lot of these details.
要考虑很多这些细节的一件事。


[2025-05-05 10:44:43]:
You can think about quite a lot like age range. For example, Is there any particular reason you've broken down the age range into 19 to 353655 and more than 55?
你可以考虑很多年龄范围。例如，您有什么特别的原因将年龄范围分为 19 到 353655 岁和 55 岁以上？


[2025-05-05 10:45:02]:
The 19 to I think the 19 to 60, sorry, 35 is like a young adult.
我认为 19 到 60 岁，对不起，35 岁就像一个年轻人。


[2025-05-05 10:45:12]:
And over 55 with like.
超过 55 人与同类。


[2025-05-05 10:45:17]:
Of older people.
老年人。


[2025-05-05 10:45:25]:
Yeah.
是的。


[2025-05-05 10:45:29]:
Maybe I can add more detail about the age. Yeah, I don't know. I mean, I just asked the question because.
也许我可以添加更多关于年龄的细节。是的，我不知道。我的意思是，我问这个问题只是因为。


[2025-05-05 10:45:39]:
And you want is there do, is there kind of like standard ways to breakdown these age ranges?
你想要有没有，有没有类似标准的方法来细分这些年龄范围？


[2025-05-05 10:45:46]:
I also have seen different ways.
我也看到了不同的方式。


[2025-05-05 10:45:51]:
19 to 35 sounds like a very big gap to me. Same.
19 到 35 岁对我来说听起来是一个非常大的差距。相同。


[2025-05-05 10:45:58]:
Sender that's 66 to 55.
发件人为 66 到 55。


[2025-05-05 10:46:00]:
So usually it should be like 19 to 24 and then 25 to 33.
所以通常应该是 19 到 24 岁，然后是 25 到 33 岁。


[2025-05-05 10:46:07]:
Or maybe it's 18 actually, 'cause we could have people who were 18 and over can do it. So yeah, we could have someone who's 18. That's OK.
或者实际上可能是 18 岁，因为我们可以让 18 岁及以上的人来做这件事。所以，是的，我们可以有一个 18 岁的人。没关系。


[2025-05-05 10:46:17]:
OK.
还行。


[2025-05-05 10:46:21]:
25
25


[2025-05-05 10:46:22]:
25 to 30
25 至 30


[2025-05-05 10:46:24]:
A 34 and 35 to 44.
A 34 和 35 到 44。


[2025-05-05 10:46:32]:
Keep going up to 110 exactly. I've had a very long one that goes that way to 100. OK.
继续精确到 110。我有过一次很长的 100 次。还行。


[2025-05-05 10:46:46]:
So if I can choose the.
所以如果我可以选择。


[2025-05-05 10:46:50]:
For different people.
适合不同的人。


[2025-05-05 10:46:53]:
Change the user testing, Maybe I can choose different like.
更改用户测试，也许我可以选择不同的 like。


[2025-05-05 10:47:00]:
Different age.
不同的年龄。


[2025-05-05 10:47:07]:
Maybe I choose one like 18 to 24 and choose 124 to 6 earth. Sorry 34
也许我选择像 18 到 24 这样的一个，然后选择 124 到 6 个地球。对不起 34


[2025-05-05 10:47:15]:
18 to 24 and choose 124 to 6 Earth. Sorry 34.
18 到 24，然后选择 124 到 6 地球。对不起 34 岁。


[2025-05-05 10:47:17]:
Like that?
诸如此类？


[2025-05-05 10:47:19]:
I think you can just recruit.
我认为你可以直接招募。


[2025-05-05 10:47:24]:
And whoever know what participants you get and you can't usually decide.
谁知道你会得到什么参与者，你通常无法决定。


[2025-05-05 10:47:31]:
So I don't need to pick one for each one. I don't think it's ethical that you pick participant OK, Do not participate, OK?
所以我不需要为每个 API 选择一个。我认为你选择参与者 OK， Do not participate， OK是不道德的。


[2025-05-05 10:47:47]:
Yeah, I mean, I think it's probably good to get a reasonable range of different people. But yes, I think the human's right, especially whoever turns up and is over is 18 or over. They all end up doing it.
是的，我的意思是，我认为找到合理范围的不同人可能是件好事。但是，是的，我认为人类是对的，尤其是无论谁出现并结束，都是 18 岁或以上。他们最终都这样做了。


[2025-05-05 10:48:00]:
OK.
还行。


[2025-05-05 10:48:02]:
And I think there's a frequency of visit. Be interesting to know I guess.
而且我认为访问频率很高。我猜知道很有趣。


[2025-05-05 10:48:08]:
Can you scroll up to the top again? Yeah.
你能再次向上滚动到顶部吗？是的。


[2025-05-05 10:48:13]:
Technology proficiency.
技术熟练程度。


[2025-05-05 10:48:17]:
Yeah, everything is it.
是的，一切都是这样。


[2025-05-05 10:48:21]:
When you got the system usability stuff that you're going to adjust a bit based on what you want and through and what's further down, like what's in Section C?
当你有了系统可用性的东西时，你要根据你想要的东西进行一些调整，然后进一步调整，比如 C 部分的内容？


[2025-05-05 10:48:33]:
Technical performance.
技术性能。


[2025-05-05 10:48:39]:
I'm not sure.
我不确定。


[2025-05-05 10:48:45]:
System stability? Do you just mean like if it crashes or not?
系统稳定性？你只是说它是否崩溃吗？


[2025-05-05 10:48:51]:
Which one?
哪一个？


[2025-05-05 10:48:53]:
System stability there, yes.
是的，系统稳定性在那里。


[2025-05-05 10:48:57]:
Maybe like the outline not matching all other things?
也许就像大纲不匹配所有其他东西一样？


[2025-05-05 10:49:07]:
Would that would that be accuracy? I don't know. I think maybe system stability is a bit ambiguous what that means. If I was reading it, I would think it means.
那会是准确性吗？我不知道。我认为也许系统稳定性有点模棱两可。如果我在读它，我会认为它的意思是。


[2025-05-05 10:49:16]:
Does the computer crash and has to be restarted? What is the software kind of putting up error messages all over the place? Because this sometimes when I like take photo, use the system for the like the capture post and for the next page it will like a black page.
计算机是否崩溃并且必须重新启动？到处张贴错误消息的软件是什么？因为有时当我喜欢拍照时，使用系统就像拍摄帖子一样，而下一页它会像黑色页面一样。


[2025-05-05 10:49:38]:
But I'm not sure why, so I try to fix it.
但我不确定为什么，所以我试着修复它。


[2025-05-05 10:49:48]:
Yeah, I don't know. I feel like we know. I'm not sure it is, but we know if the system's not working and if there's technical issues, I don't know if we need to ask people.
是的，我不知道。我觉得我们知道。我不确定是不是，但我们知道系统是否无法正常工作，是否存在技术问题，我不知道我们是否需要询问人们。


[2025-05-05 10:49:59]:
Better. Can you know that? Oh, OK, probably. I'll probably just delete that, but unless you want you think it's an important one.
更好。您能知道吗？哦，好吧，可能吧。我可能会删掉它，但除非你愿意，否则你会认为这是一个重要的问题。


[2025-05-05 10:50:08]:
I think I agree with what Andrew said. OK, Yeah, let's just focus on what we want to hear from them, which is did they?
我想我同意 Andrew 所说的。好的，是的，让我们只关注我们想从他们那里听到什么，他们是哪一个？


[2025-05-05 10:50:18]:
What was the experience like? So accuracy of recognition is good to get their feedback on that real time feedback.
那次经历是怎样的？因此，识别的准确性有利于获得他们对实时反馈的反馈。


[2025-05-05 10:50:25]:
System performance.
系统性能。


[2025-05-05 10:50:27]:
On real time feedback. So is that just?
实时反馈。那么，这公正吗？


[2025-05-05 10:50:31]:
Yeah, I don't know what do you mean by that.
是的，我不知道你这是什么意思。


[2025-05-05 10:50:36]:
OK.
还行。


[2025-05-05 10:50:42]:
So should I delay this part?
那么我应该推迟这部分吗？


[2025-05-05 10:50:47]:
No, I think I'm just accuracy of recognition. I think it makes sense to ask that.
不，我认为我只是识别的准确性。我认为这样问是有道理的。


[2025-05-05 10:50:53]:
Some people can be judging how well the outline kind of follows their pose. OK, real time, the system performance in the following aspects, real time feedback. I'm not quite sure what you mean by that. I could interpret it quite a few different ways. Yeah, like we talk about that before. Maybe the online not matching of the.
有些人可以判断轮廓在多大程度上符合他们的姿势。好的，实时的，系统性能在以下几个方面，实时反馈。我不太确定你这是什么意思。我可以用很多不同的方式来解释它。是的，就像我们之前谈到的那样。也许在线不匹配的。


[2025-05-05 10:51:20]:
Human's body.
人体。


[2025-05-05 10:51:21]:
Maybe you will use a sentence, a proper question so that people can raise, not just so I mean like more details. Maybe you say, oh, how do you feel like the real time?
也许你会用一句话，一个合适的问题，这样人们就可以提出来，而不仅仅是我的意思是喜欢更多的细节。也许你会说，噢，你觉得自己是怎样的真实时光？


[2025-05-05 10:51:39]:
Of your outline, I don't know.
关于你的大纲，我不知道。


[2025-05-05 10:51:42]:
OK, You can have a sentence like you have a statement like the system was able to reliably recognize my outline. And then you could have a strongly disagree to strongly agree scale. That's one way to do it. Or you have a question, but you just, I think for that one, I just want you to be a bit more clear about what you're actually asking because to me, I'm I could interpret it in a few different ways.
好的，你可以有一个句子，比如你有一个陈述，比如系统能够可靠地识别我的大纲。然后你可能会有一个 Very Different 到 Very agree scale。这是一种方法。或者你有一个问题，但你只是，我想对于这个问题，我只是希望你更清楚地知道你实际上在问什么，因为对我来说，我可以用几种不同的方式来解释它。


[2025-05-05 10:52:08]:
So have a think about that and we can find that, OK.
所以想一想，我们就会发现，好吧。


[2025-05-05 10:52:14]:
And then you've got a thing on. What technical issues did you encounter?
然后你就有事情要做。您遇到了哪些技术问题？


[2025-05-05 10:52:22]:
If any, I might not know it, but so you could put if any there 'cause they might not have any technical issues.
如果有的话，我可能不知道，但你可以把如果有的话放在那里，因为他们可能没有任何技术问题。


[2025-05-05 10:52:31]:
We don't wanna force them to ticket box. They don't actually have any issues.
我们不想强迫他们去买票。他们实际上没有任何问题。


[2025-05-05 10:52:41]:
So yes, I have a question too there. What technical issues did you encounter? I just put in brackets there somewhere, if any.
所以，是的，我也有一个问题。您遇到了哪些技术问题？我只是在某处放了括号，如果有的话。


[2025-05-05 10:53:14]:
Do you understand what I mean? Yes. OK, just checking.
你明白我的意思吗？是的。好的，只是检查一下。


[2025-05-05 10:53:22]:
And then educational impact.
然后是教育影响。


[2025-05-05 10:53:44]:
These are always tricky questions.
这些总是棘手的问题。


[2025-05-05 10:53:50]:
If that's enough, if I, if my paper won't like focus on the educational impact, I'm not sure because it's just the three questions.
如果这足够，如果我，如果我的论文不喜欢关注教育影响，我不确定，因为这只是三个问题。


[2025-05-05 10:54:02]:
Yeah, it's gonna be a very rough indication, I think. Yes. And I feel like this might even be interview questions you can ask them. Or do you think the system will help you, make you more interested in learning about animals? And then you ask them why, if they said a little bit and then you and then they were able to tell you.
是的，我认为这将是一个非常粗略的迹象。是的。我觉得这甚至可能是你可以问他们的面试问题。或者您认为该系统会对您有所帮助，让您对了解动物更感兴趣？然后你问他们为什么，如果他们说了一点，然后你，然后他们能够告诉你。


[2025-05-05 10:54:26]:
The aspects that make them more interested? I think so. I think that's a good point.
让他们更感兴趣的方面是什么？我认为如此。我认为这是一个很好的观点。


[2025-05-05 10:54:32]:
'Cause I think often with these kinds of questions, 'cause you're actually quite complex questions and whenever you've got like a ticket box answer for a complex question, often the data that you get ends up not being very meaningful.
因为我经常想这类问题，因为你实际上是相当复杂的问题，每当你有一个复杂问题的答案时，你得到的数据往往最终不会很有意义。


[2025-05-05 10:54:49]:
So I think what you answered like about, we actually probably want to know why or why not, OK, It helped them develop an interest.
所以我认为你回答的内容是这样的，我们实际上可能想知道为什么或为什么不，好吧，它帮助他们培养了兴趣。


[2025-05-05 10:54:58]:
I remember that I write this party.
我记得我写了这个派对。


[2025-05-05 10:55:06]:
No, sorry. OK. I can put this party in the interview questions. I think so. And then you can delete it from here. Yeah.
不，对不起。还行。我可以把这个派对放在面试问题中。我认为如此。然后你可以从这里删除它。是的。


[2025-05-05 10:55:17]:
And then maybe this section A, how did you feel when matching with successful?
然后也许这个 A 部分，当你匹配成功时，你有什么感觉？


[2025-05-05 10:55:27]:
I'm not sure that's a very useful question to know the answer to.
我不确定这是一个非常有用的问题，需要知道答案。


[2025-05-05 10:55:33]:
OK. And they are also very categorical?
还行。而且他们也很明确？


[2025-05-05 10:55:40]:
Yeah.
是的。


[2025-05-05 10:55:42]:
So I think that's probably something that you probably get more useful information from talking to them in the interview about that kind of thing. So I reckon that question we could probably delete from the questionnaire.
所以我认为，在采访中与他们谈论这种事情，你可能会得到更多有用的信息。所以我认为我们可能可以从问卷中删除这个问题。


[2025-05-05 10:55:58]:
And.
和。


[2025-05-05 10:56:01]:
I would try different poses to explore more animals.
我会尝试不同的姿势来探索更多的动物。


[2025-05-05 10:56:07]:
And this could be also based on your observation data. When you observe they post a few more times.
这也可能基于您的观察数据。当你观察时，他们会多发几次帖子。


[2025-05-05 10:56:16]:
And then you can ask them, oh, why did you try to change your pose when you use the system? Or why did you try several different posts and then they might answer you all because I want to see.
然后你可以问他们，哦，为什么你在使用系统时试图改变你的姿势？或者为什么你尝试了几个不同的帖子，然后他们可能会回答你，因为我想看看。


[2025-05-05 10:56:31]:
XYZ because I felt XYZ so this could also go into the interviews and same with question 3. This experience makes me more likely to visit. Then you can ask them to what extent do you think?
XYZ 是因为我觉得 XYZ，所以这也可以进入面试，问题 3 也是如此。这种经历使我更有可能去参观。那你可以问他们你觉得到什么程度？


[2025-05-05 10:56:36]:
XYZ because I felt XYZ so these could also go
XYZ 因为我觉得 XYZ，所以这些也可以去


[2025-05-05 10:56:49]:
Maybe that's also a questionnaire question. Yeah, I think that third one, I'll say one and two probably interview. I think the third one could be worth getting a ticket box answer to just 'cause sometimes it's useful to have clear evidence that like, yeah, everyone said hopefully, maybe everyone said they felt more likely to visit a museum because of this experience. I don't really know if people if that if it's true.
也许这也是一个问卷问题。是的，我认为第三个，我会说 1 和 2 可能是面试。我认为第三个问题可能值得得到一个票箱答案，因为有时有明确的证据是有用的，比如，是的，每个人都说希望如此，也许每个人都说他们因为这次经历而觉得更有可能参观博物馆。我真的不知道人们是否是真的。


[2025-05-05 10:57:16]:
That people actually, I mean, people will always almost that people will almost always be nice.
实际上，我的意思是，人们几乎总是会很友善。


[2025-05-05 10:57:25]:
And take and take. Strongly agree, even if in reality it doesn't make that much difference to them. Yeah, but I think it's still probably worth keeping that one in and in the interview you'll get.
然后拿走。强烈同意，即使实际上这对他们来说并没有太大区别。是的，但我认为在你得到的面试中保留这个可能仍然是值得的。


[2025-05-05 10:57:36]:
I can comment on the reasons, OK.
我可以评论一下原因，好的。


[2025-05-05 10:57:46]:
Compared to traditional exhibits, this interactive experience makes me more likely to stop and engage.
与传统展品相比，这种互动体验让我更有可能停下来参与其中。


[2025-05-05 10:57:59]:
Yeah, I guess that's worth asking.
是的，我想这值得一问。


[2025-05-05 10:58:03]:
OK.
还行。


[2025-05-05 10:58:04]:
And then I look forward to seeing similar technology and other exhibitions.
然后我期待看到类似的技术和其他展览。


[2025-05-05 10:58:10]:
Look, I think maybe those three are OK. People can take them fairly quickly and it gives us some.
你看，我觉得也许这三个都还不错。人们可以相当快地接受它们，这给了我们一些。


[2025-05-05 10:58:19]:
Some clear evidence.
一些明确的证据。


[2025-05-05 10:58:23]:
I don't know you have any thoughts about those ones? Are you OK with them or do you feel like they should be in the interview as well? I think I'm OK with them, but I feel like most people would answer like agree or likely like positive answers. I think so, yeah. I think you're OK.
我不知道你对那些有什么想法吗？你对他们没意见吗，或者你觉得他们也应该参加面试？我认为我对他们没有意见，但我觉得大多数人的回答都是同意或可能肯定的回答。我认为是的，是的。我觉得你没事。


[2025-05-05 10:58:40]:
People are generally genuine. Most time start again. Most people tend to be nice. And if we're saying we made this new technology, do you like it? They almost always go, yeah.
人们通常是真诚的。大多数时候重新开始。大多数人往往都很友善。如果我们说我们创造了这项新技术，您喜欢它吗？他们几乎总是去，是的。


[2025-05-05 10:58:55]:
Even if they might like it a bit, but they're not really that into it. So I think these questions are probably not gonna be. We can't really necessarily believe them, but I think.
即使他们可能有点喜欢它，但他们并不是真的那么喜欢它。所以我认为这些问题可能不会成为现实。我们不一定真的相信他们，但我认为。


[2025-05-05 10:59:05]:
It's still good to get an indication. I think if people really don't like it then they will tell us so.
得到一个指示还是好的。我认为如果人们真的不喜欢它，那么他们会告诉我们。


[2025-05-05 10:59:12]:
You know, it'll, it'll give us some evidence, even if it's not particularly convincing or strong.
你知道的，它会，它会给我们一些证据，即使它不是特别令人信服或有力。


[2025-05-05 10:59:19]:
I but if we combine it with the interviews then it it's it could be useful.
我，但如果我们将它与采访结合起来，那么它可能会很有用。


[2025-05-05 10:59:27]:
And then the open feedback.
然后是公开的反馈。


[2025-05-05 10:59:35]:
Describe his experiences.
请描述一下他的经历。


[2025-05-05 10:59:39]:
I feel like that that's a question is probably a good interview one better better cover than interview.
我觉得这个问题可能是一个很好的采访，一个比采访更好的封面。


[2025-05-05 10:59:46]:
You can just ask them describe what you just did or how you felt.
你可以让他们描述你刚刚做了什么或你的感受。


[2025-05-05 10:59:52]:
Or maybe you can ask them to think aloud when they try the system even. But that will be a lot of data for you to analyze afterwards. I mean, you can ask very open questions towards the beginning, something like, tell me about that experience. What was that like? OK Is that why they'll give you adjectives? Yeah. And that's also a good way to start the interview, starting with this very easy question.
或者，也许您可以要求他们在尝试该系统时大声思考。但这将是大量数据供您稍后分析。我的意思是，你可以在一开始就问一些非常开放的问题，比如，跟我说说那次经历。那是什么感觉？好吧，这就是他们会给你形容词的原因吗？是的。这也是开始面试的好方法，从这个非常简单的问题开始。


[2025-05-05 11:00:22]:
OK, so they can recall I just I just did this and I felt.
好的，这样他们就可以记得我只是做了这个，我感觉。


[2025-05-05 11:00:30]:
OK, yeah, and I think my usual.
好的，是的，我想我平常的。


[2025-05-05 11:00:35]:
The normal, I think a good interview technique is to start with very open questions like that. Tell me about that experience and then gradually you get more specific. But if you start with a very general questions, then it gives people a chance to talk about what they think is important. Sorry, sounds like a high. Yeah, it was really good. I really, but I really got frustrated with the pose or something like that. So then they are saying that the pose tracking was important to them and that was something I noticed.
我认为，正常的采访技巧是从这样非常开放的问题开始。跟我说说那次经历，然后逐渐地，你会变得更具体。但是，如果你从一个非常笼统的问题开始，那么它就会让人们有机会谈论他们认为重要的事情。对不起，听起来有点高。是的，这真的很好。我真的，但我真的对这个姿势或类似的东西感到沮丧。所以他们说姿势跟踪对他们很重要，这是我注意到的。


[2025-05-05 11:01:04]:
Whereas if you start off by saying something specific, tell me about the experience of the pose detection.
而如果你先说一些具体的事情，请告诉我姿势检测的经验。


[2025-05-05 11:01:10]:
They're already going on. The researcher wants to know about the post detection system. I'm gonna talk about that. It's so it's better to let them talk about what they felt was important first, and then if we need to know specifics, then we can ask them later. But always start more open and then get more specific.
他们已经在继续了。研究人员希望了解后检测系统。我要谈谈这个。因此，最好让他们先谈谈他们认为重要的事情，然后如果我们需要了解具体细节，我们可以稍后询问他们。但总是从更开放开始，然后变得更具体。


[2025-05-05 11:01:30]:
So when you're ordering the questions, just try and I would suggest you order them in that way. Yeah, I agree with Andrew, OK.
因此，当您订购问题时，请尝试一下，我建议您以这种方式订购它们。是的，我同意 Andrew 的观点，好的。


[2025-05-05 11:01:42]:
So tell the individual differences you can find or how people are very different when they use this same system. So that the follow up question that you tailored to this participant could also be based on what they provided in their first.
因此，请告诉您可以找到的个体差异，或者当人们使用相同的系统时，他们是如何非常不同的。因此，您为该参与者量身定制的后续问题也可以基于他们在第一个问题中提供的内容。


[2025-05-05 11:02:02]:
Answer in their answer to the first question.
在他们对第一个问题的回答中回答。


[2025-05-05 11:02:05]:
Exactly. So someone says that the colors on screen with the things that they notice the most and they were completely focused on the colors. Then you can have follow up questions about the colors and why the colors were really interesting or not interesting or whatever. OK. And other people might not mention color at all and so you don't necessarily need to ask them about it.
完全。所以有人说，屏幕上的颜色是他们最注意的事物，他们完全专注于颜色。然后你可以对颜色以及为什么颜色真的很有趣或不有趣等等进行后续问题。还行。而其他人可能根本没有提到颜色，因此您不一定需要询问他们。


[2025-05-05 11:02:27]:
Yeah, it's a good way to learn.
是的，这是一种很好的学习方式。


[2025-05-05 11:02:30]:
Yes, often the things that people notice and the things that people are interested in that quite different what we think they will be interested in.
是的，人们注意到的事物和人们感兴趣的事物通常与我们想象他们会感兴趣的事物完全不同。


[2025-05-05 11:02:38]:
OK.
还行。


[2025-05-05 11:02:40]:
So if I use different 5 different versions follow up question, should I upload the different version question to the essay kiss?
那么如果我使用不同的 5 个不同版本的后续问题，我应该将不同版本的问题上传到论文之吻吗？


[2025-05-05 11:02:59]:
No.
不。


[2025-05-05 11:03:01]:
The only specify the.
唯一指定。


[2025-05-05 11:03:06]:
Several like guiding questions, OK. And when people ask, when people answer like question one and then you based on their answer, you ask follow up questions around question 10, OK, Yeah. So you have to sorry.
有几个像引导性问题，好的。当人们提问时，当人们回答问题 1 时，然后根据他们的回答回答，你围绕问题 10 提出后续问题，好的，是的。所以你得抱歉。


[2025-05-05 11:03:28]:
I was going to say, and then you also tracked the time and then you were like maybe it's already 5 minutes past and I'm going to jump to question two of my interview question list. And then you will go to question 2. And then based on their answers, you can decide whether you want to ask or you don't need to ask a lot of questions and what follow up questions.
我本来想说的，然后你也跟踪了时间，然后你觉得也许已经过去了 5 分钟，我要跳到我的面试问题列表中的第二个问题。然后你会去问题 2。然后根据他们的回答，您可以决定是要问还是不需要问很多问题以及后续问题。


[2025-05-05 11:03:49]:
So you don't need to submit the follow up questions because that's dynamic. Oh, OK.
所以你不需要提交后续问题，因为这是动态的。哦，好的。


[2025-05-05 11:03:58]:
Exactly. So you probably have a relatively small number of questions that you will ask everyone, such as what was that experience like and whatever other specific questions that we really want answers to from everyone. But then as you answers for each of their answers, you might go, oh, that's interesting. Tell me more about the colors. Why did you find them interesting? Thank you.
完全。因此，您可能会问每个人相对较少的问题，例如那次经历是什么样的，以及我们真正希望每个人回答的任何其他具体问题。但是，当你回答他们的每个答案时，你可能会说，哦，这很有趣。请详细介绍一下颜色。您为什么觉得它们很有趣？谢谢。


[2025-05-05 11:04:23]:
So you have to.
所以你必须这样做。


[2025-05-05 11:04:24]:
The viewer, you've got to be listening to what they say and be prepared to follow up with relevant questions. OK, I'll try, I'm not good at the tagging.
观众，您必须倾听他们所说的话，并准备好跟进相关问题。好吧，我试试，我不擅长标记。


[2025-05-05 11:04:37]:
That's probably why you're doing this subject I guess is this is one thing hopefully you will learn out of this subject is like 2 interviews. This is semi structured interviews. I don't know if you've talked about them in any of your other subjects, but the structured interviews where like there is a list of questions and you always ask everyone exactly the same questions in the same order. And then there's semi structured interviews, which is what where this what this is. We have a list of questions, but we follow up depending on what.
这可能就是你做这个主题的原因，我想这是希望你能从这个主题中学到的一件事，就像 2 次面试一样。这是半结构化访谈。我不知道你是否在其他主题中谈过这些问题，但结构化的访谈就像有一系列问题，你总是以相同的顺序向每个人提出完全相同的问题。然后是半结构化访谈，这就是 What where this What What This Is What What This Is What This Is What This What This Is What我们有一个问题清单，但我们会根据问题进行跟进。


[2025-05-05 11:05:07]:
Responses are OK.
回答还可以。


[2025-05-05 11:05:11]:
And when you hear the answers, you might be really indeed curious about why they thought that. So you feel like I can't think of any follow up questions because that's because you haven't hear the answers yet, right?
当你听到答案时，你可能真的很好奇他们为什么会这样想。所以你觉得我想不出任何后续问题，因为那是因为你还没有听到答案，对吧？


[2025-05-05 11:05:29]:
OK if you want to you can. I saw someone give a demonstration once where they used I think it was ChatGPT or one of these AI things to it played the role of an interviewee.
好的，如果你愿意，你可以。我看到有人曾经做过一个演示，他们使用的地方我认为是 ChatGPT 或这些 AI 东西之一，它扮演了受访者的角色。


[2025-05-05 11:05:45]:
So and they use the voice interaction.
所以他们使用语音交互。


[2025-05-05 11:05:49]:
Talking to it and say what? And set it up and say you. I want you to play the role of someone who's at a museum interviewed about their experience using a new system.
与它交谈并说什么？然后设置它并说你。我希望你扮演一个在博物馆采访的人，了解他们使用新系统的经验。


[2025-05-05 11:05:58]:
And then and it's and then it just kind of gives plausible sounding answers to the questions that you ask it. So if you want to just kind of practice to get a feel for what it might be like, that could work the demo. I haven't done that, but I yeah, this person gave was pretty convincing. So I wouldn't spend days and days getting open AI set up to do that if it's difficult, but if it's easy, it could be something to do or either that or you just practice on friends and family and.
然后，它就是为你提出的问题给出听起来合理的答案。因此，如果您只是想进行某种练习来感受它可能是什么样子，那可以在演示中使用。我没有那样做，但我是的，这个人给出的非常有说服力。所以，如果这很困难，我不会花几天又几天来设置开放的 AI 来做到这一点，但如果它很容易，它可以是可以做的事情，或者要么那样，要么你只是在朋友和家人身上练习。


[2025-05-05 11:06:32]:
Yeah, do it. That's right. I know. Practice, run with them, OK? I know the people like who use the ChatGPT Swiss for relationships.
是的，去做吧。没错。我知道。和他们一起练习，跑步，好吗？我认识那些使用 ChatGPT Swiss 建立关系的人。


[2025-05-05 11:06:45]:
It's funny.
这很有趣。


[2025-05-05 11:06:49]:
You'd say, yeah, I don't want that. I don't want more of this
你会说，是的，我不想要那样。我不想再这样了


[2025-05-05 11:06:57]:
Maybe in the future that's we will be having relationships like that.
也许在未来，我们会有这样的关系。


[2025-05-05 11:07:00]:
Should I Should I add any questions in the survey?
我应该在调查中添加任何问题吗？


[2025-05-05 11:07:10]:
Should I add any questions in the survey? Because I guess it's not enough because maybe we just have?
我应该在调查中添加任何问题吗？因为我想这还不够，因为也许我们刚刚有？


[2025-05-05 11:07:22]:
1010 questions in the survey now because we delayed three parts. I think that's OK honestly, 'cause I think people get surveyed fatigue 'cause these days every time you do anything straight away, OK. And so I think the most of the interesting information I think like you, I think you're gonna get from the interviews and the surveys just kind of a pack up and you want that to be something that can pretty much.
现在调查中有 1010 个问题，因为我们推迟了三个部分。老实说，我认为这没问题，因为我认为人们会感到疲劳，因为现在每次你立即做任何事情时，都很好。所以我认为，我认为大多数有趣的信息，我认为你从采访和调查中获得的信息，只是有点像打包，你希望这些信息几乎可以。


[2025-05-05 11:07:49]:
Take through in a few minutes, no more. They've already given quite a lot of time. They've played with the system. You've spoken to them, you know, in an interview, which might take 5 or 10 minutes.
几分钟内完成，不多。他们已经付出了相当多的时间。他们玩弄了这个系统。你知道，你在面试中和他们谈过，这可能需要 5 到 10 分钟。


[2025-05-05 11:08:00]:
We definitely don't want to be giving them anything that's gonna take longer than a couple of minutes, OK, After that, OK.
我们绝对不想给他们任何花费超过几分钟的东西，好吧，在那之后，好吧。


[2025-05-05 11:08:08]:
Yeah, and.
是的，而且。


[2025-05-05 11:08:11]:
And short and useful, focus just the useful questions. Don't ask anything that is irrelevant. Yeah.
简短而有用，只关注有用的问题。不要问任何无关紧要的事情。是的。


[2025-05-05 11:08:21]:
And how about the interior questions?
那么内部问题呢？


[2025-05-05 11:08:24]:
Adjust the prepare for questions before the test.
调整 准备问题 在考试前。


[2025-05-05 11:08:31]:
So we already have a list of questions. We move from the questionnaires to the interview questions. Yeah. And then I think I've seen this interview question list once. And I also suggesting maybe you could have, firstly, you could have a pilot of five participants.
所以我们已经有了一个问题清单。我们从问卷转向访谈问题。是的。然后我想我已经看过这个面试问题列表一次了。我还建议，也许你可以，首先，你可以有一个 5 名参与者的试点。


[2025-05-05 11:08:54]:
And then you can also from there you will get a sense of whether some of the questions are useful as in and I can update the questions.
然后，您还可以从那里了解某些问题是否有用，例如，我可以更新问题。


[2025-05-05 11:09:06]:
Evoke useful responses from the participants, and you can also get a sense of whether some questions are useless.
唤起参与者的有用回答，您还可以了解某些问题是否毫无用处。


[2025-05-05 11:09:16]:
People don't know how to answer them or I think that's a good suggestion and I feel like you've got.
人们不知道如何回答这些问题，或者我认为这是一个很好的建议，我觉得你已经得到了。


[2025-05-05 11:09:24]:
Too many questions here. Like I think you could probably cut it down.
这里有太多问题了。就像我认为你可能会删减它。


[2025-05-05 11:09:29]:
To.
自。


[2025-05-05 11:09:31]:
Maybe 5 or 6.
也许是 5 或 6 个。


[2025-05-05 11:09:35]:
High level questions 'cause I think you're gonna be following up with them. I just think if you have this many questions in an interview, that's gonna take a long time.
高层次的问题，因为我认为你会跟进这些问题。我只是觉得，如果你在面试中有这么多问题，那会花很长时间。


[2025-05-05 11:09:46]:
OK. Yeah. And some of the questions for example, three emotional and over experience, you ask did using the system make your visit to the museum more enjoyable? That sounds like a leading question.
好的，是的。例如，一些问题，三个情绪化和过度的经历，您问使用该系统是否让您参观博物馆更加愉快？这听起来像是一个引导性问题。


[2025-05-05 11:10:01]:
Because you put the word enjoyable there and people might say, OK, I feel like it's a, it's kind of enjoyable and right. They would just go with the word that you provide with them. But instead you could ask.
因为你把 enjoyable 这个词放在那里，人们可能会说，好吧，我觉得这是一个，有点令人愉快和正确的。他们只会按照你提供给他们的词来做。但相反，你可以问。


[2025-05-05 11:10:20]:
Do you think in the future this system, like how this system might impact your visit to the museum? But that sounds a bit generic too. So maybe you talk about how it will impact?
您认为这个系统在未来会如何影响您对博物馆的访问吗？但这听起来也有点笼统。所以，也许您谈到了它将如何影响？


[2025-05-05 11:10:37]:
The participants in terms of their emotional experience but not telling but not asking them whether this is enjoyable or not.
参与者就他们的情感体验而言，但不告诉但不问他们这是否令人愉快。


[2025-05-05 11:10:51]:
OK. Yeah, that's a very good point. And that goes to this. One of the things we're talking about before is that people will tend to be nice usually. Yeah. And so if you say something like how awesome was the app that I made, they're gonna go, yeah, it was really awesome. So it's important to just have very balanced, neutral kind of questions.
好的，是的，这是一个很好的观点。这就是这个。我们之前讨论的一件事是，人们通常会倾向于友善。是的。所以，如果你说我制作的应用程序有多棒，他们就会说，是的，它真的很棒。因此，提出非常平衡、中立的问题很重要。


[2025-05-05 11:11:12]:
Such as tell me about the experience with using the app.
比如告诉我使用该应用程序的体验。


[2025-05-05 11:11:16]:
How does the? How do you think the app? How do you think applications like this might influence your?
怎么样？你觉得这个应用怎么样？您认为此类应用程序可能会对您的应用程序产生什么影响？


[2025-05-05 11:11:25]:
Future visits to the museum.
将来参观博物馆。


[2025-05-05 11:11:29]:
You could say, you could say, So would you be more or less likely to visit if there were more of these kinds of exhibits, things like that? So you just always trying to be balanced and not always framing things in a positive way.
你可以说，你可以说，那么，如果有更多这样的展品，你或多或少会去参观吗？所以你总是试图保持平衡，而不是总是以积极的方式构建事情。


[2025-05-05 11:11:47]:
OK.
还行。


[2025-05-05 11:12:13]:
Maybe I can try to find five people to order like the user test and can update the interviews for next week for next week meeting. OK, refine those questions and if you're practicing on people, just take a note of how long things take and keep in mind, you know if you wanna get.
也许我可以试着找五个人来订购，就像用户测试一样，并且可以更新下周的采访，以便下周的会议。好的，细化这些问题，如果你在人身上练习，只需记下事情需要多长时间，并记住，你知道你是否想要。


[2025-05-05 11:12:39]:
20-30 people, whatever it is, interviewed and if each one takes half an hour.
20-30 人，不管是什么，接受了采访，如果每个人都需要半小时。


[2025-05-05 11:12:46]:
That's a lot of time and a lot of time for people to be giving you. So I think probably we don't really want any longer than, I don't know, about 10 minutes or something like that, I suppose.
这是很多时间和人们给你的很多时间。所以我想，可能我们真的不想超过，我不知道，大约 10 分钟或类似的时间。


[2025-05-05 11:13:00]:
Yeah.
是的。


[2025-05-05 11:13:03]:
And to shorten this interview questions maybe for each of the category that you have there, maybe at most one to two questions for each category is already enough and you need to ask the really balance question for each of the category. OK, so for the emotional and over experience, what you're really interested about is you know how it impact.
为了缩短这个面试问题，也许对于每个类别，也许每个类别最多一到两个问题就足够了，你需要为每个类别提出真正平衡的问题。好的，所以对于情绪和过度体验，你真正感兴趣的是你知道它有什么影响。


[2025-05-05 11:13:32]:
Emotionally, right? And you can just ask that one question. How did you I would need to, I can't really tell you the question because I can't really specify what question that is. But as Andrew and I suggested, like could be a very balanced 1, not just always like enjoyable, like positive or negative.
在情感上，对吧？你可以只问这个问题。你怎么需要，我真的不能告诉你这个问题，因为我真的无法具体说明那是什么问题。但正如 Andrew 和我所建议的，喜欢可以是非常平衡的 1，而不仅仅是总是喜欢令人愉快，喜欢积极或消极。


[2025-05-05 11:13:57]:
Maybe you just need that one general question for each category and the rest will be following up with the participants.
也许你只需要每个类别的一个一般性问题，其余的将跟进参与者。


[2025-05-05 11:14:10]:
So is the like half hour each.
每个半小时也是如此。


[2025-05-05 11:14:15]:
Interview is too long because all my past courses have required 40 minutes for the interviews.
面试时间太长，因为我过去的所有课程都需要 40 分钟的面试时间。


[2025-05-05 11:14:24]:
So I'm not sure.
所以我不确定。


[2025-05-05 11:14:36]:
Sorry, did you say you passed? Past subjects have required you to do 40 minute interviews?
对不起，您说您通过了吗？过去的科目要求您进行 40 分钟的面试？


[2025-05-05 11:14:43]:
Yes, other other subject like the human center design requires me to the interview need to over 14 minutes.
是的，其他主题，如人类中心设计，需要我面试超过 14 分钟。


[2025-05-05 11:14:55]:
So now I it's.
所以现在我是这样。


[2025-05-05 11:14:57]:
Depending on what you're doing and who you're interviewing and where, the interview times could be anything from 5 minutes to two hours or something. But in this case, it's really simple thing. It's an app that does a particular thing. You want them to use it and then tell us about their experience. And we gotta keep in mind that they're visiting a museum, maybe they're with family, that they're not here only to do our.
根据你在做什么、面试对象和地点，面试时间可能从 5 分钟到 2 小时不等。但在这种情况下，这真的很简单。它是一个执行特定作的应用程序。您希望他们使用它，然后告诉我们他们的体验。我们必须记住，他们正在参观博物馆，也许他们是和家人在一起的，他们不仅仅是来这里做我们的事。


[2025-05-05 11:15:26]:
OK. They're giving us their time sort of for free while they're actually having a day out, hopefully doing something fun. So I think anything longer than 5 or 10 minutes is really too long, OK.
还行。他们免费给我们时间，而他们实际上是在外面玩一天，希望能做一些有趣的事情。所以我认为任何超过 5 或 10 分钟的时间都真的太长了，好吧。


[2025-05-05 11:15:40]:
OK.
还行。


[2025-05-05 11:15:43]:
I can make the I don't think we can find out what we need without. We don't need to hear the whole life story. That would be interesting, but.
我可以说 I don't think we can find out what.我们不需要听到整个人生故事。那会很有趣，但是。


[2025-05-05 11:15:52]:
OK.
还行。


[2025-05-05 11:15:58]:
So I need to do the user testing this week and prepare for next week meeting.
所以我需要在本周进行用户测试，并为下周的会议做准备。


[2025-05-05 11:16:09]:
Yeah, OK. That sounds good.
是的，好的。听起来不错。


[2025-05-05 11:16:14]:
And hopefully you can shake off this cold.
希望你能摆脱这种感冒。


[2025-05-05 11:16:19]:
Thank you.
谢谢。


[2025-05-05 11:16:21]:
OK.
还行。


[2025-05-05 11:16:26]:
Thank you.
谢谢。


[2025-05-05 11:16:29]:
And I guess that's all of this week meeting. Can you check the chat? OK.
我想这就是本周会议的全部内容。你能查看聊天吗？还行。


[2025-05-05 11:16:40]:
The question that he was.
他就是个问题。


[2025-05-05 11:16:44]:
Talking about that, you seem a little bit lost.
说到这里，你似乎有点迷茫。


[2025-05-05 11:16:49]:
So basically we were talk talking about what?
所以基本上我们是在谈论什么？


[2025-05-05 11:16:53]:
I think it's one of the questions in your survey question.
我认为这是您调查问题中的问题之一。


[2025-05-05 11:17:02]:
In your survey saying that, what technical issues did you encounter? And Andrew's suggestion is that you have a You add a bracket saying if any, so that people have a choice
在您的调查中，您遇到了哪些技术问题？Andrew 的建议是，你有一个 You add a bracket say if any，这样人们就有选择


[2025-05-05 11:17:16]:
In your survey saying that, what technical issues did you encounter? And Andrew's suggestion is that you have a You add a bracket saying if any, so that people have a choice.
在您的调查中，您遇到了哪些技术问题？Andrew 的建议是，你有一个 You add a bracket say if any，这样人们就有选择。


[2025-05-05 11:17:17]:
OK, but maybe there's no technical issue right in there in their run?
好吧，但也许他们的运行中没有技术问题？


[2025-05-05 11:17:35]:
Yeah, because I pulled the others here. So I thought maybe that mean if I need, but other means other technical issues. OK, it's not no technical issue.
是的，因为我把其他人都拉到了这里。所以我想，如果我需要的话，这可能意味着其他技术问题。好吧，这并非没有技术问题。


[2025-05-05 11:17:50]:
Yeah, be gonna be optimistic that hopefully most people the system works by the time it gets in there, we would have tested a bit. So hopefully most of the time it works OK.
是的，我们会乐观地认为，当系统进入那里时，大多数人都能正常工作，我们会进行一些测试。所以希望大多数时候它都能正常工作。


[2025-05-05 11:18:02]:
Yeah. And it's also a little bit weird that you already provide the category of the technical issues and that basically means that you very as a developer, what technical issues are there and then you're basically connecting accounts, OK, of the technical issues like their frequencies. Well, that's right. I mean, in a way maybe this is the question that could be deleted cause.
是的。还有点奇怪的是，您已经提供了技术问题的类别，这基本上意味着您作为开发人员，有哪些技术问题，然后您基本上将技术问题（例如它们的频率）的帐户连接起来。嗯，没错。我的意思是，在某种程度上，也许这就是可以删除的问题。


[2025-05-05 11:18:29]:
We're not asking. These people are not likely to be technical experts that can give us, they can find bugs that we didn't know about. We're probably gonna know what the bugs are, right, If there's bugs.
我们不是在问。这些人不太可能是可以给我们的技术专家，他们可以发现我们不知道的错误。我们可能会知道 bug 是什么，对吧，如果有 bug。


[2025-05-05 11:18:40]:
So maybe we just can delete the question and OK, we'll experience technical issues, we'll see you. But if I delay this question, I just have one question in the technological performance.
所以，也许我们可以删除问题，好吧，我们会遇到技术问题，我们再见。但是，如果我推迟这个问题，我只有一个关于技术性能的问题。


[2025-05-05 11:19:01]:
Yeah, we just gotta focus on what we want to learn. So we is it important for us that we know what their opinion of the accuracy was?
是的，我们只需要专注于我们想学习的东西。那么，我们知道他们对准确性的看法对我们来说很重要吗？


[2025-05-05 11:19:12]:
Or is that something we already know and we can test kind of objectively without?
或者我们已经知道了，我们可以客观地测试它？


[2025-05-05 11:19:18]:
OK. And we should only be asking them things that they are uniquely placed to tell us about?
还行。我们应该只问他们一些他们有独特位置可以告诉我们的事情。


[2025-05-05 11:19:27]:
So maybe the technical performance is not something we actually ask them. We just generally say what was the experience like some of them might say, yeah, it big, it was a big glitchy or something.
所以，也许技术性能并不是我们真正问他们的事情。我们通常只是说那次经历，就像他们中的一些人可能会说的那样，是的，它很大，这是一个很大的故障之类的。


[2025-05-05 11:19:39]:
And we can take that into account. But yeah.
我们可以考虑到这一点。但是是的。


[2025-05-05 11:19:43]:
I think that's it could be a this question could be killed. I also don't think that doesn't really like our priorities.
我认为这可能是一个可以被杀死的问题。我也不认为这真的不喜欢我们的优先事项。


[2025-05-05 11:19:55]:
In the user testing.
在用户测试中。


[2025-05-05 11:19:58]:
It might emerge as a scene in your qualitative analysis, like some people might say like this is glitchy as what you're suggesting. And then that's when you know, summarize all their people. Some people think there is some technical performance issues.
它可能会在你的定性分析中出现，就像有些人可能会说的那样，这就像你所说的那样有问题。然后你知道，总结他们所有人。有些人认为存在一些技术性能问题。


[2025-05-05 11:20:20]:
In terms of.
就而言。


[2025-05-05 11:20:23]:
Making a dedicated question for it. Maybe that's not our priority. Oh, OK, I got it. I think some of the things that might be interesting.
为它提出一个专门的问题。也许这不是我们的首要任务。哦，好吧，我明白了。我认为一些事情可能会很有趣。


[2025-05-05 11:20:35]:
And some of the issues that might come up is that people don't see the connection between the shape that they make and the shape of the database or something. Yeah, but that's something that's hard to ask in a questionnaire format. So I think basically what we're saying here is let's talk about these things in the interview and not ask potentially very complicated questions in the questionnaire where people will either get confused or they'll just take a box to get rid of the questionnaire. Yeah.
可能会出现的一些问题是，人们看不到他们创建的形状与数据库的形状或其他东西之间的联系。是的，但这是很难用问卷形式问的问题。所以我认为，基本上我们在这里要说的是，让我们在访谈中讨论这些事情，而不是在问卷中提出可能非常复杂的问题，这样人们要么会感到困惑，要么只是拿一个盒子来摆脱问卷。是的。


[2025-05-05 11:21:05]:
Went up a car that actually doesn't really make much.
上了一辆实际上并不真正赚多少钱的车。


[2025-05-05 11:21:10]:
OK.
还行。


[2025-05-05 11:21:17]:
Yes. And I think also once you do a pilot study or two with your friends and stuff and.
是的。而且我认为，一旦你和你的朋友一起做了一两次试点研究，然后。


[2025-05-05 11:21:23]:
You'll start to.
你会开始的。


[2025-05-05 11:21:26]:
You'll start to, yeah, you'll you'll realize some of these things and you'll start to find where the interesting questions are that give you something useful. Oh, OK, yeah.
你会开始，是的，你会意识到其中的一些事情，你会开始发现有趣的问题在哪里，给你带来一些有用的东西。哦，好的，是的。


[2025-05-05 11:21:41]:
Cool. Alright, so that sounds like a plan. You're gonna keep revising that?
凉。好吧，这听起来像是一个计划。你要继续修改它吗？


[2025-05-05 11:21:52]:
So any other feedback of the surrender of the saw?
那么，关于交出锯子的其他反馈吗？


[2025-05-05 11:22:10]:
So is there anything else you wanted to ask? No, that's all very useful feedback.
那么，你还有什么想问的吗？不，这些都是非常有用的反馈。


[2025-05-05 11:22:18]:
Good. OK, that's good. That's why we're here. So we'll see you all. See. I'll see you both next week. OK. See you next Monday. See you. See you then. Good luck, Johan. Bye. Bye. Bye. Bye.
好。好的，这很好。这就是我们在这里的原因。我们再见。看。下周见。还行。下周一见。再见。那时见。祝你好运，Johan。再见。再见。再见。再见。



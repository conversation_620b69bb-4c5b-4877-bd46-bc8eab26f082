[2025-04-28 10:31:33]:
Hi.
你好。


[2025-04-28 10:31:37]:
Hello.
你好。


[2025-04-28 10:31:54]:
Hi, it's a real long time no say.
嗨，这真的是很长一段时间没有发言权。


[2025-04-28 10:32:03]:
Yeah, sorry. How are you?
是的，对不起。你好吗？


[2025-04-28 10:32:09]:
It's good. It's been a really long holiday for me because all my classes are on Monday and Friday, so I already have three weeks off.
很好。这对我来说是一个非常长的假期，因为我所有的课程都在周一和周五，所以我已经有三周的假期了。


[2025-04-28 10:32:22]:
Lucky as a student. Lucky.
作为一名学生，我很幸运。幸运。


[2025-04-28 10:32:27]:
And during the break I played a few games.
在休息期间，我踢了几场比赛。


[2025-04-28 10:32:34]:
That's great. You have some rest, yes.
真棒。你休息一下，是的。


[2025-04-28 10:32:40]:
So what's the news on the museum project?
那么，博物馆项目的消息是什么呢？


[2025-04-28 10:32:45]:
OK, let me share my.
好的，让我分享一下我的。


[2025-04-28 10:32:50]:
Spring
春天


[2025-04-28 10:32:50]:
Spring, and I just want to apologize in advance as well. I'm gonna have to leave at about 20 past or so 'cause I've gotta get to another building by 10:30. OK, I'm sorry, I can sleep very quickly.
春天，我也想提前道歉。我得在 20 点左右离开，因为我必须在 10：30 之前到达另一栋楼。好的，对不起，我可以很快地睡着。


[2025-04-28 10:33:17]:
OK.
还行。


[2025-04-28 10:33:23]:
Yeah, at the last meeting I talked with Yuan about redesign my result page and let me open my system.
是的，在上次会议上，我和 Yuan 谈了重新设计我的结果页面，让我打开我的系统。


[2025-04-28 10:33:53]:
Yes, it looks like the snow.
是的，它看起来像雪。


[2025-04-28 10:33:54]:
Ye yes, it looks like
是的，看起来是这样的


[2025-04-28 10:33:57]:
Yes, I feel like my old version of my old version is.
是的，我觉得我的旧版本就是我的旧版本。


[2025-04-28 10:34:05]:
Was too small. I feel like my own. Like if it were showing a real museum it would be.
太小了。我感觉就像我自己的。就像它展示的是一个真正的博物馆一样。


[2025-04-28 10:34:14]:
Wouldn't be good for the visitors like this. For example if I matched with a tiny bird, the picture was so tiny I couldn't really see what it was. So I update the whole visual style of my system and made it looks.
对这样的访客来说可不是好事。例如，如果我匹配一只小鸟，图片太小了，我真的看不清它是什么。因此，我更新了系统的整个视觉样式并使其看起来更好。


[2025-04-28 10:34:34]:
Looks more modern like this.
看起来像这样看起来更现代。


[2025-04-28 10:34:39]:
And.
和。


[2025-04-28 10:34:48]:
Yes.
是的。


[2025-04-28 10:34:53]:
And a new update of my system on the information page.
并在信息页面上对我的系统进行了新的更新。


[2025-04-28 10:35:02]:
Sorry it's a little bit soon. Yes, I ate the Gemini AI language model from Google. After you get the result it will be.
抱歉，时间有点快。是的，我吃了 Google 的 Gemini AI 语言模型。得到结果后，它就会。


[2025-04-28 10:35:18]:
Automatically created some information about it and at first I used. I used open app, but the open eyes response was actually faster, but because I'm using the free version so I could only use it twice per day. So I switch to the Google language model and I also ate.
自动创建了一些关于它的信息，起初我使用了。我使用了 open 应用程序，但 open eyes 的响应实际上更快，但是因为我使用的是免费版本，所以我每天只能使用两次。所以我切换到 Google 语言模型，我也吃了。


[2025-04-28 10:35:43]:
Image API on the info page.
信息页面上的 Image API。


[2025-04-28 10:35:47]:
Is a open. Is a open resource image website.
是开放的。是一个开放资源的图像网站。


[2025-04-28 10:35:53]:
And it shows what the animals looks like in the real world. I got this idea when I was labeled the animal pictures. I realized that some of them didn't look like didn't look the same as I remembered. So I thought, oh like.
它显示了动物在现实世界中的样子。当我被贴上动物图片的标签时，我有了这个想法。我意识到他们中的一些看起来和我记忆中的不一样。所以我想，哦，就像。


[2025-04-28 10:35:55]:
And I
而我


[2025-04-28 10:36:16]:
Maybe the specimens and the real animals looks a bit different, so I thought maybe I can put some real real world animals on this page.
也许标本和真实的动物看起来有点不同，所以我想也许我可以在这个页面上放一些真实世界的动物。


[2025-04-28 10:36:29]:
But this part is not very good yet, because sometimes the website doesn't have the picture from the animals so.
但是这部分还不是很好，因为有时网站没有动物的图片。


[2025-04-28 10:36:38]:
That's something I still need to improve.
这是我仍然需要改进的地方。


[2025-04-28 10:36:43]:
And.
和。


[2025-04-28 10:36:46]:
I just noticed you could just ask the question about Least Concern. Is that something to do with whether they're endangered or not?
我刚刚注意到你可以直接问关于 Least Concern 的问题。这与他们是否濒临灭绝有关吗？


[2025-04-28 10:36:48]:
I just noticed you could just ask the question about least
我刚刚注意到你可以问至少


[2025-04-28 10:36:56]:
Yes.
是的。


[2025-04-28 10:36:58]:
But it's from the AI so I'm not sure it's correct or it's a mistake.
但它来自 AI，所以我不确定它是正确的还是错误的。


[2025-04-28 10:37:10]:
And.
和。


[2025-04-28 10:37:17]:
Finally, I want to talk about the matching system. Last time you and I discussed the museum at the museum that the final matching let me try to again like this.
最后，我想谈谈匹配系统。上次你我讨论博物馆的时候，那个最后的配对让我试了又一次，像这样。


[2025-04-28 10:37:35]:
The result, the result didn't feel very perfect. Like for example, sometimes the person's pose and the animal didn't really look alike and sometimes the matching score was very low. So because right now I'm using the whole moments for matching and.
结果，结果感觉不是很完美。例如，有时人的姿势和动物看起来并不真正相似，有时匹配分数非常低。所以，因为现在我正在利用整个时刻进行匹配和。


[2025-04-28 10:37:59]:
There are also other ways like.
还有其他方法，例如。


[2025-04-28 10:38:02]:
Sorry I forgot the name but I.
对不起，我忘记了名字，但我忘记了。


[2025-04-28 10:38:09]:
Wrote a script.
写了一个剧本。


[2025-04-28 10:38:10]:
Wrote a
写了一个


[2025-04-28 10:38:13]:
I wrote a script to test which one works best.
我编写了一个脚本来测试哪一个效果最好。


[2025-04-28 10:38:28]:
Yes, here are three different ways to for the post matching.
是的，这里有三种不同的帖子匹配方法。


[2025-04-28 10:38:37]:
But the results were.
但结果是。


[2025-04-28 10:38:40]:
Not great.
不是很好。


[2025-04-28 10:38:44]:
This way actually give a very high score, but when you look at the match they don't really match at all.
这种方式实际上会给出非常高的分数，但是当你查看比赛时，它们根本不匹配。


[2025-04-28 10:39:00]:
Yeah, that's my challenge now.
是的，这就是我现在的挑战。


[2025-04-28 10:39:04]:
Sorry, why there are three results here? Yeah, that's three different like 3 different way to.
对不起，为什么这里有三个结果？是的，这是三种不同的方式，就像 3 种不同的方式。


[2025-04-28 10:39:17]:
To match, yes.
匹配，是的。


[2025-04-28 10:39:21]:
Based on three different calculations, yes, Three different algorithms, I think. Yeah, but.
基于三种不同的计算，是的，我认为是三种不同的算法。是的，但是。


[2025-04-28 10:39:33]:
But the results still not very good and I try to find the best way to the best way to matching my system.
但结果仍然不是很好，我试图找到最佳方法来匹配我的系统。


[2025-04-28 10:39:47]:
Yeah, it's surprisingly.
是的，这很令人惊讶。


[2025-04-28 10:39:53]:
Yeah, I think it's because the three ways doing like a global, global matching, I try to understand.
是的，我认为这是因为我试图理解这三种方式，就像全球匹配一样。


[2025-04-28 10:40:08]:
Yeah, I guess there's different ways that shapes can be similar we might perceive.
是的，我想我们可能会以不同的方式感知到形状的相似性。


[2025-04-28 10:40:15]:
Whether it's the amount of internal area or the actual outline.
无论是内部面积的数量还是实际轮廓。


[2025-04-28 10:40:23]:
Because you can rotate things as well.
因为你也可以旋转东西。


[2025-04-28 10:40:27]:
I might have shaped like this.
我可能已经塑造成这样了。


[2025-04-28 10:40:31]:
And
和


[2025-04-28 10:40:32]:
And bird is shaped like my head on the side.
鸟的形状像我的头在侧面。


[2025-04-28 10:40:36]:
Then it might not detect the similarity of support. I guess different algorithms would detect similarity.
然后，它可能无法检测到 support 的相似性。我猜不同的算法会检测到相似性。


[2025-04-28 10:40:42]:
And
和


[2025-04-28 10:40:43]:
And some wouldn't, some wouldn't. And if you look at the outlines, the 1st and the second ones, they don't really wrap around the animal exactly. You can see there are still gaps between the animal and the lion, especially the first one. I don't think the green line is really.
有些人不会，有些人不会。如果你看一下轮廓，第一条和第二条，它们并没有真正准确地包裹住动物。你可以看到动物和狮子之间仍然存在差距，尤其是第一个。我不认为绿线真的是。


[2025-04-28 10:41:07]:
Capturing the outline of the skeleton.
捕获骨架的轮廓。


[2025-04-28 10:41:11]:
Is that skeleton?
那是骨架吗？


[2025-04-28 10:41:16]:
No, I guess it's.
不，我想是的。


[2025-04-28 10:41:20]:
I'm not sure it's like a twice.
我不确定这就像两次。


[2025-04-28 10:41:27]:
It looks like it's got the outline of the zoom. Yeah, of one of the yeah, 1 of this animal.
看起来它有缩放的轮廓。是的，是其中之一，这种动物的 1。


[2025-04-28 10:41:38]:
Yes, there are five in a row and then that's the online of one of them.
是的，连续有五个，然后就是其中一个的在线。


[2025-04-28 10:41:43]:
If you know, honey.
如果你知道的话，亲爱的。


[2025-04-28 10:41:45]:
Yeah.
是的。


[2025-04-28 10:41:48]:
Actually, also young and one small thing that is probably easy to fix. I think like the outline on your original pose, it seems like it's shifted to the left and shrunk. Is it possible to just have it so that it sits right over your head and shoulders? I'm trying to fix it. OK, right. That's just a little bug, right? Yes.
其实，也很年轻，一个小事可能很容易解决。我觉得就像你原来的姿势上的轮廓一样，它似乎向左移动并缩小了。有没有可能让它正好戴在你的头上和肩膀上？我正在努力修复它。好的，对。这只是一个小错误，对吧？是的。


[2025-04-28 10:42:15]:
And I guess it's the same for these other ones, because maybe that first one is a good match, but in a bit of the image that it's, yeah.
我想其他这些也是一样的，因为也许第一个很匹配，但在一点图像上，是的。


[2025-04-28 10:42:26]:
Because I maybe I just.
因为我也许我只是。


[2025-04-28 10:42:30]:
Write this script very quickly, but in my system it's matched, just matched of the animals.
编写这个脚本非常快，但在我的系统中它是匹配的，只是匹配到动物。


[2025-04-28 10:42:46]:
Yeah, that's all I guess.
是的，我猜就这些了。


[2025-04-28 10:42:51]:
OK, this is a update of my design.
好的，这是我设计的更新。


[2025-04-28 10:43:00]:
Maybe 1 next step that you can do is to try to.
也许您可以做的下一步是尝试。


[2025-04-28 10:43:06]:
Align the green outline with the person or with the animal.
将绿色轮廓与人或动物对齐。


[2025-04-28 10:43:13]:
More, I guess more precisely.
更多，我猜得更准确。


[2025-04-28 10:43:21]:
OK.
还行。


[2025-04-28 10:43:31]:
And I noticed that in the more information page.
我在更多信息页面中注意到了这一点。


[2025-04-28 10:43:36]:
I notice that the animal generated by the photos of the animal is small.
我注意到动物照片生成的动物很小。


[2025-04-28 10:43:43]:
Actually that end, yes.
实际上，是的。


[2025-04-28 10:43:47]:
Once the fox are the specimen, is the eagle the bird?
一旦狐狸成为标本，鹰就是鸟吗？


[2025-04-28 10:43:53]:
Because.
因为。


[2025-04-28 10:43:55]:
This oops some special animals can cannot search on the website.
这可惜一些特殊的动物无法在网站上搜索。


[2025-04-28 10:44:05]:
So I also need to fix that. Maybe I use other websites or maybe I can use the AI to make a picture, I'm not sure. What should I do?
所以我也需要解决这个问题。也许我使用其他网站，或者我可以使用 AI 来制作图片，我不确定。我该怎么办？


[2025-04-28 10:44:24]:
You can display, I guess, a map, a very simplistic one, that pinpoint is the one that you have underneath that shows where this specimen locates in the museum. It's like floor plan. And then you have like a red circle that tells the viewer where is where this animal is at.
我猜，你可以展示一张地图，非常简单的地图，那个点就是你下面的那个，它显示了这个标本在博物馆中的位置。这就像平面图。然后你有一个红色的圆圈，告诉观众这只动物在哪里。


[2025-04-28 10:44:50]:
Yeah, maybe you don't need a photo because people already see what it looks like, OK.
是的，也许你不需要照片，因为人们已经看到了它的样子，好吧。


[2025-04-28 10:45:04]:
MMM.
嗯。


[2025-04-28 10:45:11]:
So at the moment the database of images is just all the images that you're searching for the outline. I mean, that's all the.
所以目前，图像数据库只是你正在搜索的所有图像，即大纲。我的意思是，就这些。


[2025-04-28 10:45:22]:
Photos that you took yourself, That's your full databases or your photos? Or is it also using? Yes, that's all food. For myself, I took almost 500 photos from the museum.
您自己拍摄的照片，那是您的完整数据库还是您的照片？还是也在使用？是的，这就是全部食物。就我自己而言，我从博物馆拍摄了近 500 张照片。


[2025-04-28 10:45:36]:
And I still unlabeled label the pictures because a lot of birds and I actually I I'm not sure what's the name of the different birds.
我仍然没有标记图片，因为很多鸟，实际上我不确定不同鸟的名字是什么。


[2025-04-28 10:45:49]:
And I feel the label.
我感受到了这个标签。


[2025-04-28 10:45:53]:
If you but it's also hard to find because they all yeah, that's the I build a folder that I didn't know names of the birds.
如果你但也很难找到，因为他们都是 yes，那就是我构建了一个我不知道鸟名的文件夹。


[2025-04-28 10:46:08]:
So maybe I need to go to the museum at the time and.
所以也许我当时需要去博物馆。


[2025-04-28 10:46:13]:
Write the name of the picture.
写下图片的名称。


[2025-04-28 10:46:20]:
That would be good, although it sounds like any time consuming as well.
那会很好，尽管这听起来也很耗时。


[2025-04-28 10:46:24]:
I guess, at least for now, if most of them are kind of right, then it'll show that the system, the concept works.
我想，至少现在，如果他们中的大多数人都算对，那么这将表明这个系统、这个概念是有效的。


[2025-04-28 10:46:35]:
And then if we take it further to a more developed.
然后如果我们把它更进一步发展到一个更发达的。


[2025-04-28 10:46:42]:
Version then it's kind of have a proper database with accurate data in it.
Version 的 Id S 的 Id S Ly，那么它就拥有了一个包含准确数据的适当数据库。


[2025-04-28 10:46:51]:
OK.
还行。


[2025-04-28 10:46:53]:
That's all my updates of my system. I just used the 15 minutes to finish the presentation.
这就是我对系统的所有更新。我只是利用这 15 分钟完成了演示。


[2025-04-28 10:47:08]:
Thank you, young Henry.
谢谢你，年轻的亨利。


[2025-04-28 10:47:11]:
No worries.
不用担心。


[2025-04-28 10:47:13]:
Did you hear back from Sam about the requirement for this semester?
您是否收到了 Sam 关于本学期要求的回复？


[2025-04-28 10:47:20]:
Yes.
是的。


[2025-04-28 10:47:24]:
I thought maybe I.
我想也许是我。


[2025-04-28 10:47:26]:
Don't need to write a report, but I can.
不需要写报告，但我可以。


[2025-04-28 10:47:33]:
Yeah, that's my To Do List to try to understand what the same e-mail said.
是的，这就是我的待办事项列表，以尝试理解同一封电子邮件的内容。


[2025-04-28 10:47:40]:
Sam basically said was like Johan doesn't need to submit a full on dissertation by this semester even though a report is a requirement for those who only take one semester and.
山姆基本上说，就像约翰不需要在本学期之前提交完整的论文，即使报告是那些只参加一个学期的人的要求。


[2025-04-28 10:47:56]:
And he was saying that the deliverables for this semester really depends on us as supervisors. And then the mark for, I mean the yes, the mark for this semester will be the same as the mark that you I will receive in the next semester when her dissertation is graded.
他说，本学期的可交付成果真的取决于我们作为主管。然后，我的意思是，是的，这学期的分数将与你我在下学期她的论文评分时获得的分数相同。


[2025-04-28 10:48:19]:
At that time.
在那个时候。


[2025-04-28 10:48:22]:
That makes sense.
这是有道理的。


[2025-04-28 10:48:25]:
So.
所以。


[2025-04-28 10:48:29]:
I think.
我认为。


[2025-04-28 10:48:31]:
What did we say? I think didn't we have a like a project proposal?
我们说了什么？我想我们不是有一个类似项目的建议吗？


[2025-04-28 10:48:37]:
We did. We had. We had a timeline.
我们做到了。我们做到了。我们有一个时间表。


[2025-04-28 10:48:41]:
Yes, so.
是的。


[2025-04-28 10:48:44]:
Based on the original plan.
根据原来的计划。


[2025-04-28 10:48:47]:
This is roughly yes, this one.
这大致是肯定的，这个。


[2025-04-28 10:48:50]:
These two weeks are the weeks that we should come up with a user testing plan.
这两周是我们应该制定用户测试计划的几周。


[2025-04-28 10:48:58]:
And maybe conduct several pilot study too.
也许还可以进行几次试点研究。


[2025-04-28 10:49:04]:
And then Sam was suggesting maybe we could leave the user testing to the next semester.
然后 Sam 建议也许我们可以将用户测试留到下学期。


[2025-04-28 10:49:12]:
But I also remember that our original discussion was like it could be an iterative testing. We could test the system now and then make further refinement to it after we receive the results from the testing and then basically make improvements next semester or maybe do something else depending on how the results are.
但我也记得，我们最初的讨论就像它可以是一个迭代测试。我们可以不时地测试系统，然后在收到测试结果后对其进行进一步改进，然后基本上在下学期进行改进，或者根据结果做其他事情。


[2025-04-28 10:49:39]:
That sounds good to me. Feel like that it's getting pretty close now in this situation. We're into a state where I feel like you could get some meaningful information back from people.
这听起来不错。感觉在这种情况下，现在已经非常接近了。我们正处于一种状态，我觉得你可以从人们那里获得一些有意义的信息。


[2025-04-28 10:49:50]:
Like it's working enough that we could get a sense of whether the idea is worth exploring further or.
就像它足够有效，我们可以了解这个想法是否值得进一步探索。


[2025-04-28 10:49:59]:
What else it might need? Yeah.
它还可能需要什么？是的。


[2025-04-28 10:50:01]:
So I think coming up with some kind of plan for initial user study would be good. And then by the end of semester we could just have a write up of like what we a family, a summary of like what have you made so far you what features does it have and how does it work? And what have you found out from the user studies so far and not then following that, what?
所以我认为为初始用户研究制定某种计划会很好。然后到学期末，我们可以写一篇关于我们一家人的文章，总结到目前为止你做了什么，它有什么特点，它是如何工作的？到目前为止，您从用户研究中发现了什么，然后没有遵循，什么？


[2025-04-28 10:50:25]:
What is the plan for next semester?
下学期有什么计划？


[2025-04-28 10:50:32]:
OK, then I gotta, I gotta, there's my, I'm gonna throw my two cents in and then walk out the door 'cause I've gotta go. But does that make sense to you?
好吧，那我得，我得，有我的，我要把我的两分钱扔进去，然后走出门，因为我得走了。但这对你有意义吗？


[2025-04-28 10:50:36]:
OK, then I gotta, I'm gonna, there's my, I'm gonna throw my two cents in and then and
好的，那我得，我要去，这是我的，我要把我的两分钱投入进去，然后


[2025-04-28 10:50:45]:
Yeah, it's good. OK, So maybe you and you can kind of work you out a bit more of the details of what you'll do for the next week or two.
是的，这很好。好的，也许你和你可以稍微详细地了解一下你在接下来的一两周里要做什么。


[2025-04-28 10:50:57]:
I trust you to do that, Andrew. Is this.
我相信你会这样做的，安德鲁。是这个。


[2025-04-28 10:51:04]:
Sorry when you send
抱歉，当您发送时


[2025-04-28 10:51:04]:
Sorry, when you send an e-mail to me saying that you need to leave early, is that Uluru? Uluru time Uluru.
对不起，当您给我发电子邮件说您需要提前离开时，那是乌鲁鲁吗？乌鲁鲁时间乌鲁鲁。


[2025-04-28 10:51:15]:
A place in Australia because it's like half an hour earlier.
在澳大利亚的一个地方，因为它就像半小时前一样。


[2025-04-28 10:51:21]:
Maybe. So did I think this read the meeting that I've got to go to kind of came up?
或。那么，我有没有觉得我必须参加的会议是怎么回事？


[2025-04-28 10:51:27]:
I think last minute so.
我想最后一刻是这样。


[2025-04-28 10:51:29]:
Yeah, because when you wrote the e-mail you were saying like 10/10/20 you need to leave by 10:20. And I was like, the meeting hasn't even started yet. Oh, I see. Well, it's probably, I think I probably don't have the excusable LaRue. I think it's probably just my brain was working 'cause I think I don't know what it was. I can't explain Anyway, I'm sorry.
是的，因为当你写电子邮件时，你说的是 10 年 10 月 20 日，你需要在 10：20 之前离开。我当时想，会议甚至还没有开始。哦，我明白。嗯，可能，我想我可能没有情有可原的 LaRue。我想这可能只是我的大脑在工作，因为我想我不知道那是什么。我无法解释 反正，我很抱歉。


[2025-04-28 10:51:55]:
I do have to go.
我得走了。


[2025-04-28 10:51:57]:
Yeah. So I'll see you later. I'll see you next time. See you. See you next time.
是的。所以稍后再见。下次见。再见。我们下次再见。


[2025-04-28 10:52:02]:
Thank you, Andrew.
谢谢你，安德鲁。


[2025-04-28 10:52:12]:
OK.
还行。


[2025-04-28 10:52:16]:
So my.
所以我的。


[2025-04-28 10:52:20]:
Fix the outline to make it just even even better. Now it's already very close, but maybe you can align with the shape of the animal even better. But also maybe come out with an initial evaluation plan so that we can get some initial feedback like for the previous weeks. It's me, you know, who's been giving you advice on, you know, UI designs.
修复轮廓，使其更加出色。现在它已经非常接近了，但也许你可以更好地与动物的形状对齐。但也可能会提出一个初步评估计划，这样我们就可以像前几周一样获得一些初步反馈。是我，你知道的，一直在给你 UI 设计方面的建议。


[2025-04-28 10:52:50]:
That would be the functionality OK.
那将是功能 OK。


[2025-04-28 10:52:55]:
So maybe you will have some new ideas once you have some actual participants. I forgot that I have all new idea of the matching system, but it's a little bit complex. I forgot that doesn't all.
因此，一旦你有一些真正的参与者，也许你会有一些新的想法。我忘记了我对匹配系统有全新的想法，但它有点复杂。我忘了这还不是全部。


[2025-04-28 10:53:14]:
Because this one is on the next page so.
因为这个在下一页上。


[2025-04-28 10:53:22]:
Yeah, just a new idea of the matching system because I didn't feel.
是的，只是匹配系统的新想法，因为我没有感觉。


[2025-04-28 10:53:30]:
Like the system is not good so.
就像系统不好所以。


[2025-04-28 10:53:36]:
Can she can be your one of your participant?
她可以成为您的参与者之一吗？


[2025-04-28 10:53:40]:
Sorry.
不好意思。


[2025-04-28 10:53:43]:
You California, you can put in the chat saying that she can be your participant. Oh, maybe. Thank you.
你加利福尼亚，你可以在聊天中说她可以成为你的参与者。哦，也许吧。谢谢。


[2025-04-28 10:53:51]:
Maybe try to get 5?
也许试着得到 5 个？


[2025-04-28 10:54:11]:
Do I need to talk about the new idea?
我需要谈谈新想法吗？


[2025-04-28 10:54:41]:
Can you zoom in?
您能放大吗？


[2025-04-28 10:54:43]:
The new yes, it's a bit small.
新的是的，它有点小。


[2025-04-28 10:54:49]:
It's better.
它更好。


[2025-04-28 10:54:51]:
Yeah.
是的。


[2025-04-28 10:54:56]:
Yeah, it's like I take the segment mask of the person and animal and pull out the center lines that best show their posts.
是的，这就像我拿起人和动物的线段掩码，并拉出最能显示他们帖子的中心线。


[2025-04-28 10:55:08]:
Kind of like drawing a.
有点像画一个。


[2025-04-28 10:55:14]:
Simple, simple stick finger like this single and because we talk about that before about the skeleton.
简单、简单的棍指，就像这个单曲，因为我们之前讨论过骨骼。


[2025-04-28 10:55:17]:
Simple, simple stick
简单、简单的棒


[2025-04-28 10:55:26]:
But the but it's a little bit different. The key points is I don't just compare the stick figure like compare the NUM, compare the arms or legs. I just compare the simple stick finger rule from the different segments.
但是，它有点不同。关键是我不只是像比较 NUM 那样比较简笔画，而是比较手臂或腿。我只是比较了不同部分的简单棒指规则。


[2025-04-28 10:55:52]:
So and does it generate an online?
那么它是否会产生在线？


[2025-04-28 10:55:59]:
Yes, so it genera sorry.
是的，所以它很抱歉。


[2025-04-28 10:56:06]:
So I'm only so if we look at these pictures.
所以，如果我们看这些照片，我才会知道。


[2025-04-28 10:56:11]:
There are only skeletons.
只有骷髅。


[2025-04-28 10:56:15]:
To me, OK. Oh, OK. The blue part, yes, OK. The blue part is online that that's just an example, not the match, Yeah.
对我来说，好吧。哦，好吧。蓝色部分，是的，好的。蓝色部分在网上，这只是一个例子，不是匹配，是的。


[2025-04-28 10:56:28]:
Have you tried this before? Have you implemented this? Just my just my idea.
您以前试过吗？您是否实施了这个？只是我的想法。


[2025-04-28 10:56:37]:
Maybe
或


[2025-04-28 10:56:39]:
Just your idea, OK, Maybe actually I think since because I remember when we first started this project, you were kind of already interested in exploring the skeleton approach. Oh yes, the joints and right, yes, but it's a little bit different. So maybe if you want to use it as another alternative to generate the outline, maybe have a try.
只是你的想法，好吧，也许实际上我想，因为我记得当我们第一次开始这个项目时，你已经对探索骨架方法感兴趣了。哦，是的，关节，是的，但有点不同。因此，也许如果您想将其用作生成大纲的另一种选择，也许可以尝试一下。


[2025-04-28 10:57:08]:
It's a little bit different. This is before like I used both the skeleton and the segment, but my idea now is the.
这有点不同。这是以前我同时使用骨架和片段，但我现在的想法是。


[2025-04-28 10:57:23]:
Made a segment first and then use the segment to make a skeleton. So it's because the old idea is use the segment from you like you arm your legs. So if you because how to say that?
先制作一个 segment，然后使用该 segment 制作骨架。所以这是因为旧的想法是像武装双腿一样使用你的段落。所以如果你因为怎么说呢？


[2025-04-28 10:57:45]:
Maybe I can just match monkey because we both have the legs and arms, but my idea now is to use the segment to generate the.
也许我可以只匹配 monkey，因为我们都有腿和手臂，但我现在的想法是使用该段来生成。


[2025-04-28 10:58:00]:
The skeleton from the segment.
线段中的骨架。


[2025-04-28 10:58:03]:
So like the home and branch points and end points, does the segment have and how long is the longest the bone compared to the whole thing or what the angles where the lines meet?
那么，就像主点、分支点和终点一样，该段是否有，与整个骨骼相比，骨骼最长多长，或者线条相交的角度是多少？


[2025-04-28 10:58:23]:
Or.
或。


[2025-04-28 10:58:27]:
Are there are there more shape angles or bad angles? So we actually actually I comparing the.
是否有更多的形状角度或坏角度？所以我们实际上我比较了。


[2025-04-28 10:58:38]:
Like the sketch structure, scratch structure of the post, not just the overall shape or the detail.
就像草图结构一样，划痕结构的柱子，而不仅仅是整体形状或细节。


[2025-04-28 10:58:50]:
But just the idea because.
但只是这个想法，因为。


[2025-04-28 10:58:56]:
I like how the person is like all blue because it just looks better than.
我喜欢这个人像全蓝色的样子，因为它看起来比蓝色更好。


[2025-04-28 10:59:06]:
It's a simple outline, but in terms of the arrow inside.
这是一个简单的大纲，但就里面的箭头而言。


[2025-04-28 10:59:08]:
It's a simple outline, but in terms of the arrow
这是一个简单的大纲，但就箭头而言


[2025-04-28 10:59:11]:
I mean.
我的意思是。


[2025-04-28 10:59:14]:
Personally, I think I won't. I wouldn't show the arrows inside because that's the algorithms behind the result. Yeah I know, it's just the. I like how the animal is all covered up in a shade.
就我个人而言，我认为我不会。我不会显示里面的箭头，因为那是结果背后的算法。是的，我知道，这只是。我喜欢动物被遮住在阴凉处的样子。


[2025-04-28 10:59:35]:
And also just to clarify when you say.
而且，当你说的时候，也只是为了澄清。


[2025-04-28 10:59:40]:
Segment.
段。


[2025-04-28 10:59:43]:
What do you mean?
你是什么意思？


[2025-04-28 10:59:46]:
What segment are you talking about the online?
你说的是在线的哪个部分？


[2025-04-28 10:59:51]:
The online, yeah. And what's the relations between the arrows and the segments?
在线，是的。箭头和线段之间的关系是什么？


[2025-04-28 11:00:01]:
It's I just joined that before the meeting so it's just an example I I'm not sure how to made it.
我只是在会议之前加入的，所以这只是一个例子，我不确定如何制作。


[2025-04-28 11:00:20]:
I think you can put something in the chat, maybe you have to read.
我觉得你可以在聊天里放点东西，也许你得读一读。


[2025-04-28 11:00:29]:
I I'm not sure. No, it doesn't make sense for me.
我不确定。不，这对我来说没有意义。


[2025-04-28 11:00:43]:
Yes, that's what I mean.
是的，这就是我的意思。


[2025-04-28 11:00:50]:
But what do you mean by one second?
但是你说的一秒钟是什么意思？


[2025-04-28 11:01:01]:
Subpar
欠佳


[2025-04-28 11:01:01]:
One segment.
一个区段。


[2025-04-28 11:01:04]:
So what's one second?
那么什么是一秒呢？


[2025-04-28 11:01:20]:
Maybe it's just mean about the online because the online in the is named the segment, so sometimes I call it segmented but it's actually online.
也许这只是对在线的刻薄，因为在线中的在线被称为细分，所以有时我称它为分段，但它实际上是在线的。


[2025-04-28 11:01:36]:
Then how are you gonna generate those arrows?
那么你打算如何生成这些箭头呢？


[2025-04-28 11:01:39]:
Like.
喜欢。


[2025-04-28 11:01:45]:
Maybe it's not for.
也许不是为了。


[2025-04-28 11:01:50]:
Sure. Or for build a.
确定。或用于构建一个。


[2025-04-28 11:01:58]:
A skeleton is just a.
骨架只是一个。


[2025-04-28 11:02:04]:
You need a way to generate the errors right? Not showing them but I yes because that's just my idea so maybe I can try to wait because I.
你需要一种方法来产生错误，对吧？没有给他们看，但我是的，因为那只是我的想法，所以也许我可以试着等待，因为我。


[2025-04-28 11:02:23]:
Find something about this new idea is I'm trying to.
找到关于这个新想法的东西是我正在努力的。


[2025-04-28 11:02:28]:
This one.
这个。


[2025-04-28 11:02:34]:
Contours.
轮廓。


[2025-04-28 11:02:37]:
Shape yes, I found like a function developed by OpenCV, but I haven't tested it yet so.
形状是的，我发现它像是 OpenCV 开发的一个函数，但我还没有测试过它。


[2025-04-28 11:02:50]:
But it's AII guess this function is like used to make a segment first and match the.
但 AII 猜这个函数就像用来先制作一个片段并匹配 the。


[2025-04-28 11:03:03]:
Segment to use the like skeleton I guess.
Segment 使用类似的骨架，我猜。


[2025-04-28 11:03:12]:
Maybe I can try this first.
也许我可以先试试这个。


[2025-04-28 11:03:14]:
Who else maybe I can try
也许我还能尝试谁


[2025-04-28 11:03:18]:
Yeah, build a segment and build and build different end points.
是的，构建一个区段并构建和构建不同的端点。


[2025-04-28 11:03:28]:
So I think.
所以我认为。


[2025-04-28 11:03:32]:
It's respect for the match system. Sorry, could you Scroll down? I want to see the three pictures underneath.
这是对比赛系统的尊重。抱歉，您能向下滚动吗？我想看看下面的三张图片。


[2025-04-28 11:03:48]:
OK.
还行。


[2025-04-28 11:03:50]:
So you also have a way to calculate the matching score.
所以你也有办法计算匹配分数。


[2025-04-28 11:03:57]:
Right, so basically you can create a segment for the animal and then you create a segment for the user.
是的，所以基本上你可以为动物创建一个细分，然后为用户创建一个细分。


[2025-04-28 11:04:05]:
And.
和。


[2025-04-28 11:04:08]:
You will be able to generate its contour. I think what this function is for is it has a way to generate a contour, which means.
您将能够生成其轮廓。我认为这个函数的用途是它有一种生成轮廓的方法，这意味着。


[2025-04-28 11:04:19]:
An outline or the shape? I already made it actually, but I didn't write the coding for this system.
轮廓还是形状？实际上我已经做到了，但我没有为这个系统编写代码。


[2025-04-28 11:04:32]:
Match shapes, yes. So maybe you can just use that one. Try to use that one to find the most similar.
匹配形状，是的。所以也许你可以只使用那个。尝试使用该 1 来查找最相似的。


[2025-04-28 11:04:46]:
An animal.
一只动物。


[2025-04-28 11:04:55]:
Oh, that's.
哦，那是。


[2025-04-28 11:04:56]:
I already made like this.
我已经做了这样的。


[2025-04-28 11:05:01]:
I think this is much more prettier than just the online, OK? Actually feel the color in the online 'cause on week one or two, I actually suggest you just color up, OK? Yeah, this looks great, OK.
我认为这比网上的要漂亮得多，好吗？实际上在网上感受颜色，因为在第一周或第二周，我实际上建议你只是着色，好吗？是的，这看起来很棒，好吧。


[2025-04-28 11:05:35]:
Imagine you would have and I think the accuracy is pretty high to be honest.
想象一下你会有，老实说，我认为准确性相当高。


[2025-04-28 11:05:41]:
It's it looks more similar to the original animal than compared to the green lines that you just showed us before when Andrew was there.
它看起来更像原来的动物，而不是你之前给我们看的绿线，当时安德鲁在场。


[2025-04-28 11:05:55]:
And then I would imagine you would have the same thing for your post as a user. And then you use the match match shape function C dot match shape. You know what that function is, right? It's in the code on the web page.
然后我会想象你作为用户的帖子也会有同样的东西。然后你使用 match match shape 函数 C dot match shape。你知道那个函数是什么，对吧？它位于网页上的代码中。


[2025-04-28 11:06:16]:
Then you match the shape and then you it provides a score, right? And then you find the picture that provides you with the smallest score, which means it's the most similar.
然后你匹配形状，然后你提供一个分数，对吧？然后你找到给你提供最低分数的图片，这意味着它是最相似的。


[2025-04-28 11:06:34]:
Yeah, anyways, I go have a try with this one. OK, it looks promising. I just talk with you kinda like I can try the new way first and I can back up the version now and maybe you can have two low designs
是的，无论如何，我去试试这个。好吧，看起来很有希望。我只是和你聊聊，就像我可以先尝试新方法，然后我现在可以备份版本，也许你可以有两个低设计


[2025-04-28 11:06:57]:
Yeah. Anyways, I go have a try with this one, OK? It looks promising. Yeah, I just talk with you kinda like I can try the new way first and I can back up the version now and maybe you can have two designs.
是的。无论如何，我去试试这个，好吗？它看起来很有希望。是的，我只是和你聊聊，比如我可以先尝试新的方法，然后我现在可以备份版本，也许你可以有两个设计。


[2025-04-28 11:06:57]:
And ask the user to pick which one is better. Do you like this all color up version or do you like only the line OK?
并要求用户选择哪个更好。你喜欢这个全色版还是只喜欢 OK 这句话？


[2025-04-28 11:07:07]:
OK.
还行。


[2025-04-28 11:07:18]:
MMM.
嗯。


[2025-04-28 11:07:24]:
OK, that's all my updates actually. That's great.
好了，实际上这就是我所有的更新。真棒。


[2025-04-28 11:07:36]:
MMM
嗯


[2025-04-28 11:07:36]:
MMM MMM. So for the next time I need to.
嗯嗯。所以下次我需要这样做。


[2025-04-28 11:07:41]:
Delay the photo from the.
延迟照片从。


[2025-04-28 11:07:47]:
Unplugged
拔出


[2025-04-28 11:07:48]:
And.
和。


[2025-04-28 11:07:50]:
Fix the Allied mask of the Human.
修复 Human 的 Allied 面具。


[2025-04-28 11:07:56]:
Right, OK.
对，好的。


[2025-04-28 11:07:59]:
OK, yeah, I can try.
好的，是的，我可以试试。


[2025-04-28 11:08:09]:
See you. Nice next Monday. Thank you very much. Thank you. See you. Do you need to chat? Do you need to check anything that you can provide in the chat? Have you got it all?
再见。下周一不错。谢谢。谢谢。再见。您需要聊天吗？您需要检查可以在聊天中提供的任何内容吗？你都准备好了吗？


[2025-04-28 11:08:11]:
So you nice to next Monday. Thank you very much. Thank you. See you. Do you need to chat? Do you need to check anything that you can provide in the chat? Have you got it all
所以你下周一很高兴。谢谢。谢谢。再见。您需要聊天吗？您需要检查可以在聊天中提供的任何内容吗？你都准备好了吗


[2025-04-28 11:08:24]:
Yeah, I already replayed the UK.
是的，我已经重播了英国。


[2025-04-28 11:08:29]:
Just remind you. OK, See ya. OK. Thank you guys. See you. Have a good week. Have a good day. Bye bye
提醒你。好的，再见。好的，谢谢你们。再见。祝你一周愉快。祝你今天开心。再见


[2025-04-28 11:08:31]:
Just remind you, OK
提醒你，OK



import os
import shutil
import random
from pathlib import Path

def create_dataset_structure():
    """创建数据集目录结构"""
    base_dir = Path('animal_dataset')
    splits = ['train', 'val', 'test']
    
    # 创建主目录
    base_dir.mkdir(exist_ok=True)
    
    # 创建训练、验证和测试集目录
    for split in splits:
        (base_dir / split).mkdir(exist_ok=True)

def process_images(source_dirs, target_dir, split_ratio=(0.7, 0.15, 0.15)):
    """处理图片并分配到训练、验证和测试集"""
    target_path = Path(target_dir)
    
    # 获取所有图片文件
    image_files = []
    for source_dir in source_dirs:
        source_path = Path(source_dir)
        if source_path.exists():
            # 查找raw-img目录
            raw_img_path = source_path / 'raw-img'
            if raw_img_path.exists():
                print(f"正在处理目录: {raw_img_path}")
                image_files.extend(list(raw_img_path.glob('**/*.jpg')))
                image_files.extend(list(raw_img_path.glob('**/*.jpeg')))
                image_files.extend(list(raw_img_path.glob('**/*.png')))
    
    if not image_files:
        print("警告：没有找到图片文件！")
        return
    
    print(f"找到 {len(image_files)} 张图片")
    random.shuffle(image_files)
    
    # 计算分割点
    total = len(image_files)
    train_end = int(total * split_ratio[0])
    val_end = int(total * (split_ratio[0] + split_ratio[1]))
    
    # 分配文件
    splits = {
        'train': image_files[:train_end],
        'val': image_files[train_end:val_end],
        'test': image_files[val_end:]
    }
    
    # 复制文件到对应目录
    for split_name, files in splits.items():
        print(f"处理{split_name}集...")
        for img_path in files:
            # 创建目标目录
            target_folder = target_path / split_name / img_path.parent.name
            target_folder.mkdir(parents=True, exist_ok=True)
            
            # 复制文件
            shutil.copy2(img_path, target_folder / img_path.name)

def main():
    print("开始处理数据集...")
    
    # 创建目录结构
    create_dataset_structure()
    
    # 处理图片
    source_dirs = [
        "archive",
        "archive-2"
    ]
    target_dir = "animal_dataset"
    
    # 检查源目录是否存在
    for source_dir in source_dirs:
        if not os.path.exists(source_dir):
            print(f"警告：找不到源目录 {source_dir}")
    
    process_images(source_dirs, target_dir)
    
    print("数据集处理完成！")
    print("\n数据集统计：")
    for split in ['train', 'val', 'test']:
        split_path = Path(target_dir) / split
        total_images = sum(1 for _ in split_path.glob('**/*.*'))
        print(f"{split}集：{total_images}张图片")

if __name__ == "__main__":
    main() 
import { type NextRequest, NextResponse } from "next/server"
import { join } from "path"
import { writeFile } from "fs/promises"
import { exec } from "child_process"
import { promisify } from "util"
import { v4 as uuidv4 } from "uuid"

const execAsync = promisify(exec)

export async function POST(request: NextRequest) {
  try {
    const formData = await request.formData()
    const image = formData.get("image") as File

    if (!image) {
      return NextResponse.json({ error: "No image provided" }, { status: 400 })
    }

    // Create a unique ID for this processing job
    const jobId = uuidv4()

    // Save the uploaded image to a temporary location
    const bytes = await image.arrayBuffer()
    const buffer = Buffer.from(bytes)

    // Create paths for processing
    const uploadDir = join(process.cwd(), "tmp")
    const imagePath = join(uploadDir, `${jobId}-input.jpg`)
    const outputDir = join(uploadDir, jobId)

    // Save the image
    await writeFile(imagePath, buffer)

    // Run the Python script for processing
    // This would call your YOLOv8 processing script
    const { stdout, stderr } = await execAsync(
      `python scripts/process_image.py --input ${imagePath} --output ${outputDir} --model pose_matching_project/best.pt`,
    )

    if (stderr) {
      console.error("Error from Python script:", stderr)
    }

    // Read the results from the Python script
    // In a real implementation, the Python script would save the processed images and results
    // For this example, we'll simulate the response

    // Simulated response data (in a real implementation, this would come from the Python script)
    const result = {
      humanSegmentedImage: `/api/images/${jobId}/human_segmented.jpg`,
      animalImage: `/api/images/${jobId}/animal.jpg`,
      animalSegmentedImage: `/api/images/${jobId}/animal_segmented.jpg`,
      animalName: "Tiger", // This would come from the matching algorithm
      similarityScore: 87, // This would be calculated by the matching algorithm
    }

    return NextResponse.json(result)
  } catch (error) {
    console.error("Error processing image:", error)
    return NextResponse.json({ error: "Failed to process image" }, { status: 500 })
  }
}


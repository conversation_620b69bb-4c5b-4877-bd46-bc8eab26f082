import { type NextRequest, NextResponse } from "next/server"
import { join } from "path"
import { readFile } from "fs/promises"
import { existsSync } from "fs"

export async function GET(request: NextRequest, { params }: { params: { jobId: string; filename: string } }) {
  try {
    const { jobId, filename } = params

    // Validate the filename to prevent directory traversal attacks
    if (filename.includes("..") || filename.includes("/")) {
      return NextResponse.json({ error: "Invalid filename" }, { status: 400 })
    }

    // Construct the path to the image
    const imagePath = join(process.cwd(), "tmp", jobId, filename)

    // Check if the file exists
    if (!existsSync(imagePath)) {
      return NextResponse.json({ error: "Image not found" }, { status: 404 })
    }

    // Read the image file
    const imageBuffer = await readFile(imagePath)

    // Determine the content type based on the file extension
    let contentType = "image/jpeg"
    if (filename.endsWith(".png")) {
      contentType = "image/png"
    }

    // Return the image
    return new NextResponse(imageBuffer, {
      headers: {
        "Content-Type": contentType,
        "Cache-Control": "public, max-age=3600",
      },
    })
  } catch (error) {
    console.error("Error serving image:", error)
    return NextResponse.json({ error: "Failed to serve image" }, { status: 500 })
  }
}


"use client"

import { useState } from "react"
import { Card } from "@/components/ui/card"
import CaptureScreen from "@/components/capture-screen"
import ResultScreen from "@/components/result-screen"
import InstructionScreen from "@/components/instruction-screen"

export default function Home() {
  const [stage, setStage] = useState<"instruction" | "capture" | "result">("instruction")
  const [capturedImage, setCapturedImage] = useState<string | null>(null)
  const [matchedAnimal, setMatchedAnimal] = useState<{
    image: string
    segmentedImage: string
    name: string
    similarity: number
  } | null>(null)
  const [humanSegmentedImage, setHumanSegmentedImage] = useState<string | null>(null)

  const handleCapture = async (imageSrc: string) => {
    setCapturedImage(imageSrc)

    try {
      // Process the captured image on the server
      const formData = new FormData()
      const blob = await (await fetch(imageSrc)).blob()
      formData.append("image", blob)

      const response = await fetch("/api/process-image", {
        method: "POST",
        body: formData,
      })

      if (!response.ok) {
        throw new Error("Failed to process image")
      }

      const data = await response.json()
      setHumanSegmentedImage(data.humanSegmentedImage)
      setMatchedAnimal({
        image: data.animalImage,
        segmentedImage: data.animalSegmentedImage,
        name: data.animalName,
        similarity: data.similarityScore,
      })

      setStage("result")
    } catch (error) {
      console.error("Error processing image:", error)
      // Handle error state
    }
  }

  const resetExperience = () => {
    setCapturedImage(null)
    setMatchedAnimal(null)
    setHumanSegmentedImage(null)
    setStage("instruction")
  }

  return (
    <main className="flex min-h-screen flex-col items-center justify-center p-4 bg-gray-100">
      <Card className="w-full max-w-4xl p-6 bg-white shadow-lg rounded-xl">
        {stage === "instruction" && <InstructionScreen onStart={() => setStage("capture")} />}

        {stage === "capture" && <CaptureScreen onCapture={handleCapture} onCancel={resetExperience} />}

        {stage === "result" && capturedImage && matchedAnimal && humanSegmentedImage && (
          <ResultScreen
            humanImage={capturedImage}
            humanSegmentedImage={humanSegmentedImage}
            animalImage={matchedAnimal.image}
            animalSegmentedImage={matchedAnimal.segmentedImage}
            animalName={matchedAnimal.name}
            similarityScore={matchedAnimal.similarity}
            onReset={resetExperience}
          />
        )}
      </Card>
    </main>
  )
}


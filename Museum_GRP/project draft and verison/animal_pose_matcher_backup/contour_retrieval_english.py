import os
import cv2
import numpy as np
import pickle
import torch
from pathlib import Path
import matplotlib.pyplot as plt
from ultralytics import YOLO
from scipy.spatial import KDTree

class ContourRetrieval:
    """Contour Retrieval System"""
    
    def __init__(self):
        """Initialize retrieval system"""
        self.base_dir = Path('/Users/<USER>/Desktop/pose_matching_project')
        self.database_path = self.base_dir / 'database' / 'animal_contours.pkl'
        self.human_model_path = self.base_dir / 'models' / 'human_seg_model.pt'
        self.results_dir = self.base_dir / 'results'
        self.results_dir.mkdir(exist_ok=True)
        
        # Load human segmentation model
        print("Loading human segmentation model...")
        self.human_model = YOLO(self.human_model_path)
        
        # Load animal contour database
        print("Loading animal database...")
        with open(self.database_path, 'rb') as f:
            self.database = pickle.load(f)
            
        # Build KD-tree index
        print("Building retrieval index...")
        self.build_index()
        
        print("Initialization complete!")
    
    def build_index(self):
        """Build KD-tree index for fast retrieval"""
        # Calculate features for each contour
        self.features = []
        for contour in self.database['contours']:
            features = self.compute_shape_features(contour)
            self.features.append(features)
        
        # Convert feature list to numpy array
        self.features = np.array(self.features)
        
        # Handle infinite values and NaN values
        self.features = np.nan_to_num(self.features, nan=0.0, posinf=0.0, neginf=0.0)
        
        # Build KD-tree
        self.kdtree = KDTree(self.features)
    
    def compute_shape_features(self, contour):
        """Calculate shape features for contour"""
        # Calculate Hu moments
        moments = cv2.moments(contour)
        if moments['m00'] != 0:
            hu_moments = cv2.HuMoments(moments).flatten()
        else:
            hu_moments = np.zeros(7)
        
        # Safe logarithmic transformation
        log_hu = np.zeros_like(hu_moments)
        for i, hu in enumerate(hu_moments):
            if abs(hu) > 1e-7:  # Avoid taking log of zero
                log_hu[i] = -np.sign(hu) * np.log10(abs(hu))
            else:
                log_hu[i] = 0
        
        # Calculate area and perimeter ratio
        area = cv2.contourArea(contour)
        perimeter = cv2.arcLength(contour, True)
        if perimeter > 0:
            circularity = 4 * np.pi * area / (perimeter * perimeter)
        else:
            circularity = 0
        
        # Calculate contour orientation
        if len(contour) >= 5:  # Need at least 5 points to fit ellipse
            try:
                (x, y), (ma, mi), angle = cv2.fitEllipse(contour)
                aspect_ratio = ma / mi if mi > 0 else 1.0
            except:
                aspect_ratio = 1.0
                angle = 0
        else:
            aspect_ratio = 1.0
            angle = 0
        
        # Combine features
        features = np.concatenate([
            log_hu,  # Hu moments
            [circularity],  # Circularity
            [aspect_ratio],  # Aspect ratio
            [np.sin(angle * np.pi / 180), np.cos(angle * np.pi / 180)]  # Direction (sine and cosine)
        ])
        
        # Handle infinite values and NaN values
        features = np.nan_to_num(features, nan=0.0, posinf=0.0, neginf=0.0)
        
        return features
    
    def extract_human_contour(self, image_path):
        """Extract contour from human image"""
        # Use YOLO model for prediction
        results = self.human_model(image_path, verbose=False)
        
        # Get segmentation masks
        if not results or not results[0].masks:
            raise ValueError("No human contour detected")
        
        # Get human masks (select the human detection result with highest confidence)
        person_masks = []
        for r in results[0].boxes.data:
            if int(r[5]) == 0:  # 0 represents person in COCO dataset
                mask_idx = int(r[4])  # Get corresponding mask index
                if mask_idx < len(results[0].masks):
                    person_masks.append((float(r[4]), results[0].masks[mask_idx].data[0].cpu().numpy()))
        
        if not person_masks:
            raise ValueError("No human contour detected")
        
        # Use the human mask with highest confidence
        mask = max(person_masks, key=lambda x: x[0])[1]
        
        # Extract contour
        mask = (mask * 255).astype(np.uint8)
        contours, _ = cv2.findContours(mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        if not contours:
            raise ValueError("No human contour detected")
        
        # Get the largest contour
        max_contour = max(contours, key=cv2.contourArea)
        
        # Normalize contour
        x, y, w, h = cv2.boundingRect(max_contour)
        max_contour = max_contour - [x, y]
        scale = 100.0 / max(w, h)
        max_contour = (max_contour * scale).astype(np.float32)
        
        return max_contour
    
    def retrieve_similar_poses(self, image_path, top_k=1):
        """Retrieve similar animal poses"""
        # Extract human contour
        print("Extracting human contour...")
        human_contour = self.extract_human_contour(image_path)
        
        # Calculate features
        print("Computing features...")
        human_features = self.compute_shape_features(human_contour)
        
        # Search for nearest neighbors in KD-tree
        print("Searching for similar poses...")
        distances, indices = self.kdtree.query(human_features, k=top_k)
        
        # Handle single result case (k=1) - convert to array
        if top_k == 1:
            distances = np.array([distances])
            indices = np.array([indices])
        
        # Prepare results
        results = []
        for dist, idx in zip(distances, indices):
            results.append({
                'category': self.database['categories'][idx],
                'image_id': self.database['image_ids'][idx],
                'image_path': self.database['image_paths'][idx],
                'contour': self.database['contours'][idx],
                'similarity_score': 1.0 / (1.0 + dist)  # Convert distance to similarity score
            })
        
        # Visualize results
        self.visualize_results(image_path, human_contour, results)
        
        return results
    
    def visualize_results(self, image_path, human_contour, results):
        """Visualize retrieval results"""
        n_results = len(results)
        
        # For single result case, make figures larger
        if n_results == 1:
            fig, axes = plt.subplots(1, 2, figsize=(12, 6))
            fig.suptitle('Animal Pose Matching Result', fontsize=16, y=0.98)
        else:
            fig, axes = plt.subplots(1, n_results + 1, figsize=(4*(n_results + 1), 5))
            
        fig.patch.set_facecolor('white')
        
        # Display input image with contour overlay
        query_img = cv2.imread(str(image_path))
        query_img = cv2.cvtColor(query_img, cv2.COLOR_BGR2RGB)
        
        # Create a copy of input image for overlaying contour
        overlay_img = query_img.copy()
        
        # Resize human contour to match input image dimensions
        img_h, img_w = query_img.shape[:2]
        resize_factor = min(img_h, img_w) / 100.0  # Assuming contour was normalized to 100x100
        
        # Adjust contour to center of image
        resized_contour = (human_contour * resize_factor).astype(np.int32)
        center_offset_x = (img_w - resized_contour[:, 0, 0].max()) // 2
        center_offset_y = (img_h - resized_contour[:, 0, 1].max()) // 2
        centered_contour = resized_contour + [center_offset_x, center_offset_y]
        
        # Draw human contour on original image
        cv2.drawContours(overlay_img, [centered_contour], -1, (0, 255, 0), 2)
        
        # Access first index properly for both single result and multiple results
        if n_results == 1:
            ax_human = axes[0]
        else:
            ax_human = axes[0]
            
        # Show overlay image
        ax_human.imshow(overlay_img)
        ax_human.axis('off')
        ax_human.set_title('Your Pose', fontsize=14, pad=10)
        
        # Display retrieval results
        for i, result in enumerate(results):
            # Display animal image with contour overlay
            img_path = self.base_dir / 'database' / 'images' / f"{result['image_id']}.jpg"
            try:
                animal_img = cv2.imread(str(img_path))
                if animal_img is None:
                    print(f"Could not read image: {img_path}")
                    continue
                    
                animal_img = cv2.cvtColor(animal_img, cv2.COLOR_BGR2RGB)
                
                # Create copy for contour overlay
                animal_overlay = animal_img.copy()
                
                # Resize animal contour to match image dimensions
                animal_h, animal_w = animal_img.shape[:2]
                animal_resize_factor = min(animal_h, animal_w) / 100.0
                
                # Adjust contour to center of image
                animal_contour = result['contour']
                resized_animal_contour = (animal_contour * animal_resize_factor).astype(np.int32)
                animal_offset_x = (animal_w - resized_animal_contour[:, 0, 0].max()) // 2
                animal_offset_y = (animal_h - resized_animal_contour[:, 0, 1].max()) // 2
                centered_animal_contour = resized_animal_contour + [animal_offset_x, animal_offset_y]
                
                # Draw animal contour on original image
                cv2.drawContours(animal_overlay, [centered_animal_contour], -1, (0, 255, 0), 2)
                
                # Access axis properly for both cases
                if n_results == 1:
                    ax_animal = axes[1]
                else:
                    ax_animal = axes[i+1]
                
                # Show overlay
                ax_animal.imshow(animal_overlay)
                ax_animal.axis('off')
                
                # Use better title formatting for single result
                if n_results == 1:
                    title = f"{result['category'].capitalize()}\n{result['similarity_score']:.0f}% Match"
                    ax_animal.set_title(title, fontsize=14, pad=10)
                else:
                    ax_animal.set_title(f"{result['category'].capitalize()}\n{result['similarity_score']:.2f}", 
                                      fontsize=12, pad=10)
                
            except Exception as e:
                print(f"Error displaying result {i+1}: {e}")
        
        # Add similarity score between the images for single result case
        if n_results == 1 and results:
            similarity = results[0]['similarity_score'] * 100
            plt.figtext(0.5, 0.01, f'Similarity Score: {similarity:.0f}%', 
                       ha='center', fontsize=14, 
                       bbox={'facecolor':'lightgrey', 'alpha':0.5, 'pad':5})
        
        # Save visualization
        image_name = Path(image_path).stem
        plt.tight_layout()
        plt.savefig(str(self.results_dir / f"retrieval_results_{image_name}.png"), dpi=150)
        plt.close() 
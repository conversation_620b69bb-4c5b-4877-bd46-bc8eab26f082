import cv2
import numpy as np
import time
from pathlib import Path
import sys

# Import our modified English version of contour retrieval
# Remove the old import path
# sys.path.append(str(Path(__file__).parent / 'pose_matching_project'))
# from utils.contour_retrieval import ContourRetrieval

# Import the modified English version from desktop
sys.path.append('/Users/<USER>/Desktop')
from contour_retrieval_english import ContourRetrieval

class CameraRetrieval:
    """Camera Retrieval System"""
    
    def __init__(self):
        """Initialize camera retrieval system"""
        self.retrieval = ContourRetrieval()
        self.cap = None
        self.temp_dir = Path('/Users/<USER>/Desktop/pose_matching_project/temp')
        self.temp_dir.mkdir(exist_ok=True)
        
    def countdown(self, frame):
        """Display countdown"""
        for i in range(3, 0, -1):
            # Display countdown number in the center of the frame
            display_frame = frame.copy()
            cv2.putText(display_frame, str(i), (frame.shape[1]//2 - 50, frame.shape[0]//2), 
                       cv2.FONT_HERSHEY_SIMPLEX, 4, (0, 0, 255), 5)
            cv2.imshow('Camera Preview', display_frame)
            cv2.waitKey(1000)  # Wait for 1 second
            ret, frame = self.cap.read()  # Update frame
            if not ret:
                break
        
        # Display "Capture!" text
        if frame is not None:
            display_frame = frame.copy()
            cv2.putText(display_frame, "Capture!", (frame.shape[1]//2 - 100, frame.shape[0]//2), 
                       cv2.FONT_HERSHEY_SIMPLEX, 2, (0, 255, 0), 3)
            cv2.imshow('Camera Preview', display_frame)
            cv2.waitKey(500)  # Wait for 0.5 seconds
            return frame
        return None
        
    def capture_and_retrieve(self):
        """Open camera, take photo and retrieve similar poses"""
        print("Starting camera...")
        self.cap = cv2.VideoCapture(0)
        
        if not self.cap.isOpened():
            print("Could not open camera")
            return
        
        try:
            while True:
                ret, frame = self.cap.read()
                if not ret:
                    print("Could not get frame")
                    break
                    
                # Display instructions
                display_frame = frame.copy()
                cv2.putText(display_frame, "Press SPACE to start countdown", (50, 50), 
                           cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 0), 2)
                cv2.putText(display_frame, "Press 'q' to quit", (50, 100), 
                           cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 0), 2)
                
                cv2.imshow('Camera Preview', display_frame)
                key = cv2.waitKey(1) & 0xFF
                
                if key == ord('q'):
                    break
                elif key == ord(' '):
                    # Start countdown
                    captured_frame = self.countdown(frame)
                    
                    # Take photo
                    if captured_frame is not None:
                        # Save temporary image
                        temp_image = self.temp_dir / 'capture.jpg'
                        cv2.imwrite(str(temp_image), captured_frame)
                        
                        # Perform retrieval
                        print("\nStarting retrieval...")
                        try:
                            results = self.retrieval.retrieve_similar_poses(str(temp_image))
                            
                            # Display results in console
                            print("\nBest Match Result:")
                            result = results[0]  # Get the only result (best match)
                            print(f"Animal: {result['category'].capitalize()}")
                            print(f"Similarity Score: {result['similarity_score']:.0f}%")
                            
                            # Read and display result image
                            result_image = cv2.imread(str(self.retrieval.results_dir / f"retrieval_results_capture.png"))
                            if result_image is not None:
                                # Create resizable window
                                cv2.namedWindow('Animal Match Result', cv2.WINDOW_NORMAL)
                                cv2.resizeWindow('Animal Match Result', 1000, 500)  # Adjusted for side-by-side display
                                cv2.imshow('Animal Match Result', result_image)
                                
                                print("\nPress any key to continue...")
                                cv2.waitKey(0)
                                cv2.destroyWindow('Animal Match Result')
                            
                        except Exception as e:
                            print(f"Error during retrieval: {e}")
                            
        finally:
            # Release resources
            self.cap.release()
            cv2.destroyAllWindows()

def main():
    """Main function"""
    try:
        camera = CameraRetrieval()
        camera.capture_and_retrieve()
    except Exception as e:
        print(f"Program error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    main() 
import React from 'react'
import { motion } from 'framer-motion'
import { Canvas } from '@react-three/fiber'
import { OrbitControls } from '@react-three/drei'

function Model() {
  return (
    <mesh>
      <boxGeometry args={[1, 1, 1]} />
      <meshStandardMaterial color="hotpink" />
    </mesh>
  )
}

export default function Demo() {
  return (
    <section id="demo" className="min-h-screen py-20">
      <div className="container mx-auto px-4">
        <h2 className="text-4xl font-bold text-center mb-16">互动体验</h2>
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8 }}
            className="bg-gray-800/50 backdrop-blur-sm rounded-lg p-8"
          >
            <h3 className="text-2xl font-semibold mb-6">动作映射演示</h3>
            <div className="aspect-video bg-black/30 rounded-lg overflow-hidden">
              <Canvas>
                <ambientLight intensity={0.5} />
                <pointLight position={[10, 10, 10]} />
                <Model />
                <OrbitControls />
              </Canvas>
            </div>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, x: 20 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8 }}
            className="space-y-8"
          >
            <div className="bg-gray-800/50 backdrop-blur-sm rounded-lg p-6">
              <h4 className="text-xl font-semibold mb-4">动作对应关系</h4>
              <ul className="space-y-4">
                <li className="flex items-center space-x-4">
                  <span className="w-24">快速摆臂</span>
                  <span className="text-blue-400">→</span>
                  <span>蜂鸟悬停</span>
                </li>
                <li className="flex items-center space-x-4">
                  <span className="w-24">弓步前倾</span>
                  <span className="text-blue-400">→</span>
                  <span>猎豹冲刺</span>
                </li>
                <li className="flex items-center space-x-4">
                  <span className="w-24">旋转跳跃</span>
                  <span className="text-blue-400">→</span>
                  <span>袋鼠拳击</span>
                </li>
              </ul>
            </div>

            <div className="bg-gray-800/50 backdrop-blur-sm rounded-lg p-6">
              <h4 className="text-xl font-semibold mb-4">实时数据</h4>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <p className="text-sm text-gray-400">动作识别置信度</p>
                  <p className="text-2xl font-semibold">98%</p>
                </div>
                <div>
                  <p className="text-sm text-gray-400">响应延迟</p>
                  <p className="text-2xl font-semibold">16ms</p>
                </div>
              </div>
            </div>
          </motion.div>
        </div>
      </div>
    </section>
  )
} 
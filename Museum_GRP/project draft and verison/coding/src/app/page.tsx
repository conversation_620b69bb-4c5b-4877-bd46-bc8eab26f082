import React from 'react'
import { motion } from 'framer-motion'
import Demo from './components/Demo'

export default function Home() {
  return (
    <div className="min-h-screen">
      <header className="fixed top-0 left-0 right-0 z-50 bg-black/50 backdrop-blur-sm">
        <nav className="container mx-auto px-4 py-4">
          <ul className="flex items-center justify-center space-x-8">
            <li>
              <a href="#home" className="text-white hover:text-blue-400 transition-colors">首页</a>
            </li>
            <li>
              <a href="#tech" className="text-white hover:text-blue-400 transition-colors">技术介绍</a>
            </li>
            <li>
              <a href="#demo" className="text-white hover:text-blue-400 transition-colors">体验展示</a>
            </li>
            <li>
              <a href="#about" className="text-white hover:text-blue-400 transition-colors">关于我们</a>
            </li>
          </ul>
        </nav>
      </header>

      <section id="home" className="min-h-screen flex items-center justify-center relative">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          className="text-center"
        >
          <h1 className="text-5xl font-bold mb-6">创新博物馆互动装置</h1>
          <p className="text-xl text-gray-300 mb-8">体验前所未有的人机交互方式</p>
          <motion.button
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            className="bg-blue-500 hover:bg-blue-600 text-white px-8 py-3 rounded-full text-lg font-medium transition-colors"
          >
            开始体验
          </motion.button>
        </motion.div>
      </section>

      <section id="tech" className="min-h-screen py-20">
        <div className="container mx-auto px-4">
          <h2 className="text-4xl font-bold text-center mb-16">核心技术</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {[
              {
                title: '动作捕捉',
                description: '使用先进的计算机视觉技术，实时追踪33个身体关节点'
              },
              {
                title: '动作分析',
                description: '基于深度学习的动作特征提取和模式识别'
              },
              {
                title: '行为映射',
                description: '创新的人类-动物行为映射算法，实现自然的交互体验'
              }
            ].map((item, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.2 }}
                className="bg-gray-800/50 backdrop-blur-sm p-6 rounded-lg"
              >
                <h3 className="text-2xl font-semibold mb-4">{item.title}</h3>
                <p className="text-gray-300">{item.description}</p>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      <Demo />
    </div>
  )
} 
{"name": "museum-interactive", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@heroicons/react": "^2.2.0", "@react-three/drei": "^10.0.3", "@react-three/fiber": "^9.0.4", "framer-motion": "^12.4.7", "next": "15.2.0", "react": "^19.0.0", "react-dom": "^19.0.0", "three": "^0.174.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.2.0", "tailwindcss": "^4", "typescript": "^5"}}
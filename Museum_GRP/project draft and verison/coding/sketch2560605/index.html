<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>手势音乐交互</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            overflow: hidden;
            background: #000;
        }
        canvas {
            display: block;
        }
        #startButton {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            padding: 15px 30px;
            font-size: 18px;
            background: #4CAF50;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            display: none;
        }
        #instructions {
            position: fixed;
            top: 20px;
            left: 20px;
            color: white;
            font-family: Arial, sans-serif;
            background: rgba(0,0,0,0.7);
            padding: 10px;
            border-radius: 5px;
            display: none;
        }
    </style>
    <!-- P5.js 库 -->
    <script src="https://cdn.jsdelivr.net/npm/p5@1.11.3/lib/p5.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/p5@1.11.3/lib/addons/p5.sound.min.js"></script>
    <!-- 主程序文件 -->
    <script src="mySketch.js.js"></script>
</head>
<body>
    <div id="instructions">
        <h3>使用说明：</h3>
        <p>1. 允许使用摄像头</p>
        <p>2. 在摄像头前移动手指（0-10根）</p>
        <p>3. 观察粒子效果和聆听音乐变化</p>
    </div>
    <button id="startButton">点击开始体验</button>
    <script>
        window.onload = function() {
            const instructions = document.getElementById('instructions');
            instructions.style.display = 'block';
            
            // 检查浏览器是否支持所需的API
            if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
                alert('您的浏览器不支持摄像头访问，请使用现代浏览器（如Chrome、Firefox）');
                return;
            }
        }
    </script>
</body>
</html>
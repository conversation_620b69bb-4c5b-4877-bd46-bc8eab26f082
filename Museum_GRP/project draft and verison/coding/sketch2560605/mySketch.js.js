let myHandLandmarker;
let handLandmarks;
let myCapture;
let lastVideoTime = -1;
let doFlipHorizontal = true;
let cameraStarted = false;

// パーティクル配列 & 音オシレーター
let particles = [];
let osc1, osc2, osc3;
let reverb, delay, filter;
let synth;
const NUM_PARTICLES = 5000; // 常時表示するパーティクル数

// 音階とコード定義
const SCALES = {
  C_MAJOR: ['C4', 'D4', 'E4', 'F4', 'G4', 'A4', 'B4', 'C5'],
  C_MINOR: ['C4', 'D4', 'Eb4', 'F4', 'G4', 'Ab4', 'Bb4', 'C5'],
  PENTATONIC: ['C4', 'D4', 'F4', 'G4', 'A4', 'C5']
};

const CHORDS = {
  C_MAJ: ['C4', 'E4', 'G4'],
  F_MAJ: ['F4', 'A4', 'C5'],
  G_MAJ: ['G4', 'B4', 'D5']
};

// 指本数の段階を記憶し、切り替わった時にパーティクルをリセット
let currentStage = -1;  // -1: 未定義

async function preload() {
  const mediapipe_module = await import(
    "https://cdn.jsdelivr.net/npm/@mediapipe/tasks-vision/vision_bundle.js"
  );
  HandLandmarker = mediapipe_module.HandLandmarker;
  FilesetResolver = mediapipe_module.FilesetResolver;

  const vision = await FilesetResolver.forVisionTasks(
    "https://cdn.jsdelivr.net/npm/@mediapipe/tasks-vision@0.10.7/wasm"
  );

  // 両手検出用ハンドランドマーカー
  myHandLandmarker = await HandLandmarker.createFromOptions(vision, {
    numHands: 2,
    runningMode: "VIDEO",
    baseOptions: {
      delegate: "GPU",
      modelAssetPath:
        "https://storage.googleapis.com/mediapipe-models/hand_landmarker/hand_landmarker/float16/1/hand_landmarker.task",
    },
  });
}

function setup() {
  createCanvas(windowWidth, windowHeight);
  background(0);
  textSize(20);
  textAlign(CENTER, CENTER);
  fill(255);
  text("Click to start Camera", width / 2, height / 2);

  // 音のセットアップ
  osc1 = new p5.Oscillator("sine");
  osc2 = new p5.Oscillator("triangle");
  osc3 = new p5.Oscillator("square");
  
  // 新しい音響効果のセットアップ
  reverb = new p5.Reverb();
  delay = new p5.Delay();
  filter = new p5.LowPass();
  synth = new p5.PolySynth();

  // エフェクトの接続
  osc1.disconnect();
  osc2.disconnect();
  osc3.disconnect();
  synth.disconnect();

  osc1.connect(filter);
  osc2.connect(filter);
  osc3.connect(filter);
  synth.connect(filter);
  
  filter.connect(delay);
  delay.connect(reverb);
  reverb.connect();

  // エフェクトの初期設定
  reverb.set(3, 2); // リバーブタイム3秒、デケイ2秒
  delay.set(0.3, 0.5); // ディレイタイム0.3秒、フィードバック0.5
  filter.freq(2000);

  osc1.start();
  osc2.start();
  osc3.start();
  osc1.amp(0);
  osc2.amp(0);
  osc3.amp(0);

  // パーティクルを生成（常に表示）
  for (let i = 0; i < NUM_PARTICLES; i++) {
    particles.push(new Particle(random(width), random(height)));
  }
}

function mousePressed() {
  if (!cameraStarted) {
    userStartAudio(); // ブラウザの音再生許可
    myCapture = createCapture(VIDEO);
    myCapture.size(windowWidth, windowHeight);
    myCapture.hide();
    predictWebcam();
    cameraStarted = true;
  }
}

async function predictWebcam() {
  let startTimeMs = performance.now();
  if (lastVideoTime !== myCapture.elt.currentTime) {
    if (myHandLandmarker) {
      const detected = myHandLandmarker.detectForVideo(myCapture.elt, startTimeMs);
      if (detected) {
        handLandmarks = detected;
      }
    }
    lastVideoTime = myCapture.elt.currentTime;
  }
  window.requestAnimationFrame(predictWebcam);
}

function draw() {
  background(0);

  if (!cameraStarted) {
    text("Click to start Camera", width / 2, height / 2);
    return;
  }

  drawVideoBackground();
  drawHandLandmarks();

  // 両手合わせて最大10本 → 0~10段階
  if (handLandmarks && handLandmarks.landmarks && handLandmarks.landmarks.length > 0) {
    analyzeHandGesture();
  }

  // パーティクルの更新＆描画
  for (let p of particles) {
    p.update();
    p.show();
  }
}

// 映像を左右反転して表示
function drawVideoBackground() {
  push();
  if (doFlipHorizontal) {
    translate(width, 0);
    scale(-1, 1);
  }
  imageMode(CORNER);
  image(myCapture, 0, 0, width, height);
  pop();
}

// 手のランドマーク & 接続線を描画
function drawHandLandmarks() {
  if (!(handLandmarks && handLandmarks.landmarks)) return;

  const nHands = handLandmarks.landmarks.length;
  for (let h = 0; h < nHands; h++) {
    const joints = handLandmarks.landmarks[h];

    // ポイント描画
    for (let i = 0; i < joints.length; i++) {
      const x = map(joints[i].x, 0, 1, width, 0);
      const y = map(joints[i].y, 0, 1, 0, height);

      fill(255, 0, 0);
      noStroke();
      ellipse(x, y, 8, 8);

      fill(255);
      textSize(14);
      textAlign(CENTER, CENTER);
      text(i, x, y - 10);
    }
    drawConnections(joints);
  }
}

// 指が開いているかを簡易判定 (MCP→TIP)
function isFingerOpen(mcp, tip) {
  // tip.y が mcp.yよりさらに 0.07 小さいと \"開き\"
  return (tip.y < mcp.y - 0.07);
}

// 片手の開いている指を数える
function countOpenFingersOneHand(joints) {
  if (!joints || joints.length < 21) return 0;

  // 親指 (2->4), 人差し指 (5->8), 中指 (9->12), 薬指 (13->16), 小指 (17->20)
  let thumb = isFingerOpen(joints[2], joints[4]);
  let index = isFingerOpen(joints[5], joints[8]);
  let middle = isFingerOpen(joints[9], joints[12]);
  let ring = isFingerOpen(joints[13], joints[16]);
  let pinky = isFingerOpen(joints[17], joints[20]);

  return [thumb, index, middle, ring, pinky].filter(Boolean).length;
}

// 両手の指の合計を 0~10 の段階にしてアニメーションを切り替え (重複なし)
function analyzeHandGesture() {
  const nHands = handLandmarks.landmarks.length;

  let totalOpen = 0; // 0..10
  for (let i = 0; i < nHands; i++) {
    totalOpen += countOpenFingersOneHand(handLandmarks.landmarks[i]);
  }
  totalOpen = constrain(totalOpen, 0, 10);

  // 前回の段階と異なる場合にリセット＆アニメーション呼び出し
  if (totalOpen !== currentStage) {
    resetParticles(); // ★パーティクルを初期状態に戻す
    currentStage = totalOpen; // 更新

    // 10種類のアニメーションが重複しない
    switch (currentStage) {
      case 0:
        convergeParticles();
        updateSound(200, 0.4);
        break;
      case 1:
        swirlParticles();
        updateSound(300, 0.5);
        break;
      case 2:
        waveParticles();
        updateSound(350, 0.6);
        break;
      case 3:
        colorSparkParticles();
        updateSound(400, 0.6);
        break;
      case 4:
        randomDriftParticles();
        updateSound(450, 0.7);
        break;
      case 5:
        scatterParticles();
        updateSound(500, 0.7);
        break;
      case 6:
        revolveParticles();
        updateSound(550, 0.8);
        break;
      case 7:
        bounceParticles();
        updateSound(600, 0.8);
        break;
      case 8:
        vortexParticles();
        updateSound(700, 0.9);
        break;
      case 9:
        fractalParticles();
        updateSound(800, 1.0);
        break;
      case 10:
        fireworksParticles();
        updateSound(1000, 1.0);
        break;
    }
  }
}

// 以下に欠けていたアニメーション関数を実装
function convergeParticles() {
  for (let p of particles) {
    p.convergeToCenter();
  }
}

function swirlParticles() {
  for (let p of particles) {
    p.swirl();
  }
}

function waveParticles() {
  for (let p of particles) {
    p.wave();
  }
}

function colorSparkParticles() {
  for (let p of particles) {
    p.colorSpark();
  }
}

function randomDriftParticles() {
  for (let p of particles) {
    p.randomDrift();
  }
}

function scatterParticles() {
  for (let p of particles) {
    p.scatter();
  }
}

function revolveParticles() {
  for (let p of particles) {
    p.revolve();
  }
}

function bounceParticles() {
  for (let p of particles) {
    p.bounce();
  }
}

function vortexParticles() {
  for (let p of particles) {
    p.vortex();
  }
}

function fractalParticles() {
  for (let p of particles) {
    p.fractal();
  }
}

function fireworksParticles() {
  for (let p of particles) {
    p.fireworks();
  }
}

// パーティクルの状態を初期化 (位置・速度・色を再生成など)
function resetParticles() {
  particles = [];
  for (let i = 0; i < NUM_PARTICLES; i++) {
    particles.push(new Particle(random(width), random(height)));
  }
}

function drawConnections(joints) {
  const HAND_CONNECTIONS = [
    { start: 0, end: 1 },  { start: 1, end: 2 },  { start: 2, end: 3 },  { start: 3, end: 4 },
    { start: 0, end: 5 },  { start: 5, end: 6 },  { start: 6, end: 7 },  { start: 7, end: 8 },
    { start: 0, end: 9 },  { start: 9, end: 10 }, { start: 10, end: 11 },{ start: 11, end: 12 },
    { start: 0, end: 13 }, { start: 13, end: 14 },{ start: 14, end: 15 },{ start: 15, end: 16 },
    { start: 0, end: 17 }, { start: 17, end: 18 },{ start: 18, end: 19 },{ start: 19, end: 20 }
  ];
  stroke(0, 255, 0);
  strokeWeight(2);
  for (let conn of HAND_CONNECTIONS) {
    const x0 = map(joints[conn.start].x, 0, 1, width, 0);
    const y0 = map(joints[conn.start].y, 0, 1, 0, height);
    const x1 = map(joints[conn.end].x, 0, 1, width, 0);
    const y1 = map(joints[conn.end].y, 0, 1, 0, height);
    line(x0, y0, x1, y1);
  }
}

// パーティクルクラス
class Particle {
  constructor(x, y) {
    this.pos = createVector(x, y);
    this.vel = p5.Vector.random2D().mult(random(0.5, 2));
    this.size = random(3, 8);
    this.color = color(255);
  }

  update() {
    this.pos.add(this.vel);
    // バウンド
    if (this.pos.x < 0 || this.pos.x > width) this.vel.x *= -1;
    if (this.pos.y < 0 || this.pos.y > height) this.vel.y *= -1;
  }

  show() {
    noStroke();
    fill(this.color);
    ellipse(this.pos.x, this.pos.y, this.size);
  }

  // =========== 各アニメーション ===========

  convergeToCenter() {
    // 中心へ引き寄せる
    let center = createVector(width/2, height/2);
    let force = p5.Vector.sub(center, this.pos);
    force.setMag(0.2);            // 値を大きめに
    this.vel.add(force);
  }

  scatter() {
    // ランダム散乱
    let randomForce = p5.Vector.random2D().mult(random(0.5, 2));
    this.vel.add(randomForce);
  }

  swirl() {
    // 軽い回転
    let angle = 0.1;              // 角度を少し増やす
    this.applyRotation(angle, 0.3);
  }

  wave() {
    // 波運動
    this.vel.y += sin(frameCount * 0.2) * 20.3; // 強めに
  }

  colorSpark() {
    // ランダム色 + 小さな散乱
    this.color = color(random(255), random(255), random(255));
    let spark = p5.Vector.random2D().mult(random(1,3));
    this.vel.add(spark);
  }

  randomDrift() {
    // 緩やかにドリフト
    let drift = p5.Vector.random2D().mult(10.5); // 少し強め
    this.vel.add(drift);
  }

  revolve() {
    // 強めの回転
    let angle = 10.3;   // 大きく
    this.applyRotation(angle, 10.7);
  }

  bounce() {
    // 垂直反転をさらに強調
    this.vel.y *= -20;  // 倍率を上げる
    this.vel.x += random(-2, 20); // 横方向にもブレ
  }

  vortex() {
    // 渦：回転 + 中心引き寄せ
    let angle = 0.2;
    this.applyRotation(angle, 10.8);

    // 中心へのわずかな収束
    let center = createVector(width / 2, height / 2);
    let force = p5.Vector.sub(center, this.pos);
    force.setMag(10.05);   // わずかに
    this.vel.add(force);
  }

  fractal() {
    // ランダム色 & 大きく散らばる
    this.color = color(random(255), random(255), random(255));
    let force = p5.Vector.random2D().mult(random(2,5)); // 強めに
    this.vel.add(force);
  }

  fireworks() {
    // 派手に色変化 & 大きく散乱
    this.color = color(random(255), random(255), random(255));
    let spark = p5.Vector.random2D().mult(random(4,8));
    this.vel.add(spark);
  }

  // =====================================
  // 内部補助メソッド：回転を適用
  applyRotation(angle, lerpFactor) {
    let rotatedVel = createVector(
      this.vel.x * cos(angle) - this.vel.y * sin(angle),
      this.vel.x * sin(angle) + this.vel.y * cos(angle)
    );
    this.vel = p5.Vector.lerp(this.vel, rotatedVel, lerpFactor);
  }
}

// 音の制御
function updateSound(freq, amp) {
  // 基本の振動音
  osc1.freq(freq, 0.1);
  osc2.freq(freq * 0.5, 0.1);
  osc3.freq(freq * 2, 0.1);

  osc1.amp(amp * 0.3, 0.1);
  osc2.amp(amp * 0.2, 0.1);
  osc3.amp(amp * 0.1, 0.1);

  // 手の状態に応じて異なる音楽パターンを再生
  let currentScale = SCALES.C_MAJOR;
  let noteIndex = Math.floor(map(freq, 200, 1000, 0, currentScale.length));
  let note = currentScale[noteIndex % currentScale.length];
  
  // シンセサイザーで和音を演奏
  if (frameCount % 30 === 0) { // 一定間隔で演奏
    synth.play(note, amp * 0.5, 0, 0.2);
    
    // 和音を追加（確率的に）
    if (random() < 0.3) {
      let chord = random(Object.values(CHORDS));
      chord.forEach((n, i) => {
        synth.play(n, amp * 0.3, 0.1 * i, 0.3);
      });
    }
  }

  // エフェクトのパラメータを動的に変更
  let filterFreq = map(amp, 0, 1, 500, 5000);
  filter.freq(filterFreq);
  delay.feedback(map(amp, 0, 1, 0.2, 0.7));
  reverb.drywet(map(amp, 0, 1, 0.3, 0.8));
}